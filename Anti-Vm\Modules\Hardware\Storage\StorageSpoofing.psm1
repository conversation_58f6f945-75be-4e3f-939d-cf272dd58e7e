# Storage Spoofing Module - Dedicated Disk Drive and Storage Controller Spoofing
# Comprehensive storage device spoofing for VM detection bypass
# Covers SSDs, HDDs, NVMe drives, SATA controllers, and SMART data

# Import required modules
Import-Module "$PSScriptRoot\..\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Core\Utilities\Utilities.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Registry\RegistryPrivileges\RegistryPrivileges.psm1" -Force

#region Storage Profile Database

$script:StorageProfiles = @{
    SSD_NVMe = @(
        @{
            Manufacturer = "Samsung"
            Model = "Samsung SSD 980 PRO 2TB"
            Interface = "NVMe"
            FormFactor = "M.2 2280"
            Capacity = 2048
            FirmwareVersion = "5B2QGXA7"
            Controller = "Samsung Elpis"
            NANDType = "3D TLC"
            MaxSeqRead = 7000
            MaxSeqWrite = 6900
            MaxRandomRead = 1000000
            MaxRandomWrite = 1000000
            TBW = 1200
            Warranty = 5
        },
        @{
            Manufacturer = "Western Digital"
            Model = "WD Black SN850X 1TB"
            Interface = "NVMe"
            FormFactor = "M.2 2280"
            Capacity = 1024
            FirmwareVersion = "620361WD"
            Controller = "WD G2"
            NANDType = "3D TLC"
            MaxSeqRead = 7300
            MaxSeqWrite = 6600
            MaxRandomRead = 800000
            MaxRandomWrite = 1100000
            TBW = 600
            Warranty = 5
        },
        @{
            Manufacturer = "Crucial"
            Model = "Crucial P5 Plus 1TB"
            Interface = "NVMe"
            FormFactor = "M.2 2280"
            Capacity = 1024
            FirmwareVersion = "P9CR013"
            Controller = "Phison E21T"
            NANDType = "3D TLC"
            MaxSeqRead = 6600
            MaxSeqWrite = 5000
            MaxRandomRead = 650000
            MaxRandomWrite = 700000
            TBW = 600
            Warranty = 5
        }
    )
    SSD_SATA = @(
        @{
            Manufacturer = "Samsung"
            Model = "Samsung SSD 870 EVO 1TB"
            Interface = "SATA"
            FormFactor = "2.5 inch"
            Capacity = 1024
            FirmwareVersion = "SVT02B6Q"
            Controller = "Samsung MJX"
            NANDType = "3D TLC"
            MaxSeqRead = 560
            MaxSeqWrite = 530
            MaxRandomRead = 98000
            MaxRandomWrite = 88000
            TBW = 600
            Warranty = 5
        },
        @{
            Manufacturer = "Crucial"
            Model = "Crucial MX570 1TB"
            Interface = "SATA"
            FormFactor = "2.5 inch"
            Capacity = 1024
            FirmwareVersion = "M3CR045"
            Controller = "Micron"
            NANDType = "3D TLC"
            MaxSeqRead = 560
            MaxSeqWrite = 510
            MaxRandomRead = 95000
            MaxRandomWrite = 90000
            TBW = 400
            Warranty = 3
        }
    )
    HDD = @(
        @{
            Manufacturer = "Seagate"
            Model = "Seagate Barracuda 2TB"
            Interface = "SATA"
            FormFactor = "3.5 inch"
            Capacity = 2048
            FirmwareVersion = "CC43"
            Controller = "Seagate"
            RPM = 7200
            CacheSize = 256
            MaxSeqRead = 220
            MaxSeqWrite = 220
            Warranty = 2
        },
        @{
            Manufacturer = "Western Digital"
            Model = "WD Blue 1TB"
            Interface = "SATA"
            FormFactor = "3.5 inch"
            Capacity = 1024
            FirmwareVersion = "82.00A82"
            Controller = "WD"
            RPM = 7200
            CacheSize = 64
            MaxSeqRead = 150
            MaxSeqWrite = 150
            Warranty = 2
        }
    )
}

#endregion

#region Storage Profile Selection

function Get-RealisticStorageProfile {
    <#
    .SYNOPSIS
        Selects a realistic storage profile based on system configuration
    .DESCRIPTION
        Returns a comprehensive storage profile with SMART data and specifications
    #>
    [CmdletBinding()]
    param(
        [string]$PreferredType = "SSD_NVMe",
        [string]$PreferredManufacturer = "Samsung"
    )
    
    Write-ModuleLog "Selecting realistic storage profile (type: $PreferredType, manufacturer: $PreferredManufacturer)" "Debug"
    
    try {
        $selectedProfile = switch ($PreferredType.ToUpper()) {
            "SSD_NVME" { $script:StorageProfiles.SSD_NVMe | Get-Random }
            "SSD_SATA" { $script:StorageProfiles.SSD_SATA | Get-Random }
            "HDD" { $script:StorageProfiles.HDD | Get-Random }
            default { $script:StorageProfiles.SSD_NVMe | Get-Random }
        }
        
        # Filter by manufacturer if specified
        if ($PreferredManufacturer -ne "Random") {
            $manufacturerProfile = $script:StorageProfiles.$PreferredType | Where-Object { $_.Manufacturer -eq $PreferredManufacturer }
            if ($manufacturerProfile) {
                $selectedProfile = $manufacturerProfile | Get-Random
            }
        }
        
        # Generate dynamic data
        $selectedProfile.SerialNumber = New-StorageSerialNumber -Manufacturer $selectedProfile.Manufacturer -Model $selectedProfile.Model
        $selectedProfile.SmartData = Get-RealisticSMARTData -StorageType $PreferredType
        
        Write-ModuleLog "Selected storage profile: $($selectedProfile.Model) ($($selectedProfile.Interface))" "Info"
        return $selectedProfile
    }
    catch {
        Write-ModuleLog "Failed to select storage profile: $($_.Exception.Message)" "Error"
        throw
    }
}

function New-StorageSerialNumber {
    <#
    .SYNOPSIS
        Generates realistic storage serial numbers based on manufacturer patterns
    #>
    param(
        [string]$Manufacturer,
        [string]$Model
    )
    
    switch ($Manufacturer.ToLower()) {
        "samsung" {
            return "S$(Get-Random -Min 100000000000 -Max 999999999999)"
        }
        { $_ -like "*western digital*" -or $_ -eq "wd" } {
            return "WD-$(Get-Random -Min 100000000000 -Max 999999999999)"
        }
        "crucial" {
            return "$(Get-Random -Min 100000000000 -Max 999999999999)"
        }
        "seagate" {
            return "$(Get-Random -Min 100000000 -Max 999999999)"
        }
        "intel" {
            return "BTPY$(Get-Random -Min 100000000000 -Max 999999999999)"
        }
        default {
            return "$(Get-Random -Min 100000000000 -Max 999999999999)"
        }
    }
}

function Get-RealisticSMARTData {
    <#
    .SYNOPSIS
        Generates realistic SMART attribute data for storage devices
    #>
    param(
        [string]$StorageType = "SSD_NVMe"
    )
    
    $smartData = @{}
    
    # Common SMART attributes
    $smartData.Temperature = Get-Random -Min 25 -Max 45
    $smartData.PowerOnHours = Get-Random -Min 100 -Max 8760
    $smartData.PowerCycleCount = Get-Random -Min 50 -Max 1000
    $smartData.UnsafeShutdownCount = Get-Random -Min 0 -Max 50
    
    # Type-specific SMART data
    switch ($StorageType) {
        "SSD_NVMe" {
            $smartData.DataUnitsWritten = Get-Random -Min 1000 -Max 50000
            $smartData.DataUnitsRead = Get-Random -Min 2000 -Max 100000
            $smartData.HostReadCommands = Get-Random -Min 1000000 -Max 50000000
            $smartData.HostWriteCommands = Get-Random -Min 500000 -Max 25000000
            $smartData.ControllerBusyTime = Get-Random -Min 100 -Max 5000
            $smartData.CriticalWarning = 0
            $smartData.AvailableSpare = Get-Random -Min 95 -Max 100
            $smartData.PercentageUsed = Get-Random -Min 1 -Max 15
        }
        "SSD_SATA" {
            $smartData.WearLevelingCount = Get-Random -Min 1 -Max 100
            $smartData.UsedReservedBlockCount = Get-Random -Min 0 -Max 10
            $smartData.ProgramFailCount = 0
            $smartData.EraseFailCount = 0
            $smartData.RuntimeBadBlockCount = 0
        }
        "HDD" {
            $smartData.ReallocatedSectorCount = Get-Random -Min 0 -Max 5
            $smartData.CurrentPendingSectorCount = 0
            $smartData.UncorrectableSectorCount = 0
            $smartData.SpinUpTime = Get-Random -Min 4000 -Max 6000
            $smartData.SeekErrorRate = Get-Random -Min 1000000 -Max 10000000
        }
    }
    
    return $smartData
}

#endregion

#region Storage Registry Spoofing

function Invoke-StorageRegistrySpoofing {
    <#
    .SYNOPSIS
        Spoofs storage device information in Windows Registry
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting storage registry spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Storage device registry keys
        $storageKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\IDE',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\STORAGE',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\UASPSTOR',
            'HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum',
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e967-e325-11ce-bfc1-08002be10318}'  # Storage controllers
        )
        
        foreach ($keyPath in $storageKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    
                    # Check if this is a storage-related key
                    if ($properties -and (
                        $properties.Class -eq "DiskDrive" -or
                        $fullPath -like "*Disk*" -or
                        $fullPath -like "*Storage*" -or
                        $fullPath -like "*SCSI*" -or
                        $properties.Service -eq "disk"
                    )) {
                        
                        # Core storage device properties
                        Set-RegistryValue -Path $fullPath -Name "FriendlyName" -Value $StorageProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $StorageProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $StorageProfile.Manufacturer -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Provider" -Value $StorageProfile.Manufacturer -Type "String"
                        
                        # Storage identification
                        Set-RegistryValue -Path $fullPath -Name "SerialNumber" -Value $StorageProfile.SerialNumber -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Model" -Value $StorageProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Revision" -Value $StorageProfile.FirmwareVersion -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "FirmwareRevision" -Value $StorageProfile.FirmwareVersion -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ProductRevision" -Value $StorageProfile.FirmwareVersion -Type "String"
                        
                        # Interface and connection details
                        Set-RegistryValue -Path $fullPath -Name "BusType" -Value $StorageProfile.Interface -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "InterfaceType" -Value $StorageProfile.Interface -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "MediaType" -Value "Fixed hard disk media" -Type "String"
                        
                        # Capacity information
                        Set-RegistryValue -Path $fullPath -Name "Size" -Value ($StorageProfile.Capacity * 1024 * 1024 * 1024) -Type "QWord"
                        Set-RegistryValue -Path $fullPath -Name "TotalSectors" -Value ($StorageProfile.Capacity * 1953125) -Type "QWord"  # 512-byte sectors
                        
                        # Performance characteristics
                        if ($StorageProfile.MaxSeqRead) {
                            Set-RegistryValue -Path $fullPath -Name "MaxSequentialRead" -Value $StorageProfile.MaxSeqRead -Type "DWord"
                            Set-RegistryValue -Path $fullPath -Name "MaxSequentialWrite" -Value $StorageProfile.MaxSeqWrite -Type "DWord"
                        }
                        
                        if ($StorageProfile.RPM) {
                            Set-RegistryValue -Path $fullPath -Name "NominalMediaRotationRate" -Value $StorageProfile.RPM -Type "DWord"
                        } else {
                            Set-RegistryValue -Path $fullPath -Name "NominalMediaRotationRate" -Value 1 -Type "DWord"  # SSD (non-rotating)
                        }
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        Write-ModuleLog "Storage registry spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; ModifiedCount = $modifiedCount }
    }
    catch {
        Write-ModuleLog "Storage registry spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

#endregion

#region SMART Data Spoofing

function Invoke-SMARTDataSpoofing {
    <#
    .SYNOPSIS
        Spoofs SMART attribute data for storage devices
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting SMART data spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # SMART data registry paths
        $smartPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Services\disk',
            'HKLM:\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies',
            'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Perflib\009'
        )
        
        foreach ($smartPath in $smartPaths) {
            if (Test-Path $smartPath) {
                # Write SMART attributes
                foreach ($smart in $StorageProfile.SmartData.GetEnumerator()) {
                    Set-RegistryValue -Path $smartPath -Name "SMART_$($smart.Key)" -Value $smart.Value -Type "DWord"
                }
                
                # Additional storage health indicators
                Set-RegistryValue -Path $smartPath -Name "HealthStatus" -Value "Healthy" -Type "String"
                Set-RegistryValue -Path $smartPath -Name "PredictiveFailureCount" -Value 0 -Type "DWord"
                Set-RegistryValue -Path $smartPath -Name "LastSMARTUpdate" -Value ([DateTimeOffset]::Now.ToUnixTimeSeconds()) -Type "DWord"
                
                $modifiedCount++
            }
        }
        
        # Create detailed SMART attribute registry structure
        $smartDetailPath = "HKLM:\SYSTEM\CurrentControlSet\Services\disk\SMART"
        if (-not (Test-Path $smartDetailPath)) {
            New-Item -Path $smartDetailPath -Force | Out-Null
        }
        
        # Standard SMART attributes (simplified representation)
        $standardSMART = @{
            "01_ReadErrorRate" = @{ Value = 0; Threshold = 6; Status = "OK" }
            "05_ReallocatedSectorCount" = @{ Value = 0; Threshold = 36; Status = "OK" }
            "09_PowerOnHours" = @{ Value = $StorageProfile.SmartData.PowerOnHours; Threshold = 0; Status = "OK" }
            "0C_PowerCycleCount" = @{ Value = $StorageProfile.SmartData.PowerCycleCount; Threshold = 20; Status = "OK" }
            "B7_VendorSpecific" = @{ Value = 100; Threshold = 10; Status = "OK" }
            "BB_UncorrectableErrorCount" = @{ Value = 0; Threshold = 0; Status = "OK" }
            "C2_Temperature" = @{ Value = $StorageProfile.SmartData.Temperature; Threshold = 0; Status = "OK" }
            "C5_CurrentPendingSectorCount" = @{ Value = 0; Threshold = 0; Status = "OK" }
        }
        
        foreach ($attr in $standardSMART.GetEnumerator()) {
            $attrPath = "$smartDetailPath\$($attr.Key)"
            if (-not (Test-Path $attrPath)) {
                New-Item -Path $attrPath -Force | Out-Null
            }
            
            Set-RegistryValue -Path $attrPath -Name "CurrentValue" -Value $attr.Value.Value -Type "DWord"
            Set-RegistryValue -Path $attrPath -Name "Threshold" -Value $attr.Value.Threshold -Type "DWord"
            Set-RegistryValue -Path $attrPath -Name "Status" -Value $attr.Value.Status -Type "String"
        }
        
        Write-ModuleLog "SMART data spoofing completed: $modifiedCount paths modified" "Info"
        return @{ Success = $true; ModifiedCount = $modifiedCount }
    }
    catch {
        Write-ModuleLog "SMART data spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

#endregion

#region Storage Controller Spoofing

function Invoke-StorageControllerSpoofing {
    <#
    .SYNOPSIS
        Spoofs storage controller information (SATA, NVMe, etc.)
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting storage controller spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Storage controller registry paths
        $controllerPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}',  # System devices
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e96a-e325-11ce-bfc1-08002be10318}',  # HDC controllers
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{71a27cdd-812a-11d0-bec7-08002be2092f}',  # Volume devices
            'HKLM:\SYSTEM\CurrentControlSet\Enum\PCI'
        )
        
        foreach ($controllerPath in $controllerPaths) {
            if (Test-Path $controllerPath) {
                $subKeys = Get-ChildItem -Path $controllerPath -Recurse -ErrorAction SilentlyContinue
                
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    
                    # Check if this is a storage controller
                    if ($properties -and (
                        $properties.Class -eq "hdc" -or
                        $properties.Class -eq "SCSIAdapter" -or
                        $fullPath -like "*Storage*" -or
                        $fullPath -like "*SATA*" -or
                        $fullPath -like "*NVMe*" -or
                        $properties.Service -eq "storahci" -or
                        $properties.Service -eq "stornvme"
                    )) {
                        
                        # Controller identification based on storage type
                        if ($StorageProfile.Interface -eq "NVMe") {
                            Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value "Intel(R) Volume Management Device NVMe RAID Controller" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value "Intel Corporation" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value "18.1.0.1006" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @("PCI\VEN_8086&DEV_467F") -Type "MultiString"
                        } else {
                            Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value "Intel(R) Chipset SATA/PCIe RST Premium Controller" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value "Intel Corporation" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value "19.5.1.1040" -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @("PCI\VEN_8086&DEV_7AE2") -Type "MultiString"
                        }
                        
                        # Controller capabilities
                        Set-RegistryValue -Path $fullPath -Name "Capabilities" -Value 0x10 -Type "DWord"
                        Set-RegistryValue -Path $fullPath -Name "ConfigFlags" -Value 0 -Type "DWord"
                        
                        # Performance settings
                        Set-RegistryValue -Path $fullPath -Name "QueueDepth" -Value 32 -Type "DWord"
                        Set-RegistryValue -Path $fullPath -Name "MaxTransferSize" -Value 65536 -Type "DWord"
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        Write-ModuleLog "Storage controller spoofing completed: $modifiedCount controllers modified" "Info"
        return @{ Success = $true; ModifiedCount = $modifiedCount }
    }
    catch {
        Write-ModuleLog "Storage controller spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

#endregion

#region WMI Storage Spoofing

function Invoke-StorageWMISpoofing {
    <#
    .SYNOPSIS
        Spoofs storage information in WMI subsystem
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting storage WMI spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # WMI storage information paths
        $wmiPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Perflib\009",
            "HKLM:\SOFTWARE\Classes\Installer\Products",
            "HKLM:\SYSTEM\CurrentControlSet\Control\WMI\Autologger\EventLog-System"
        )
        
        foreach ($wmiPath in $wmiPaths) {
            if (Test-Path $wmiPath) {
                # Disk drive information
                Set-RegistryValue -Path $wmiPath -Name "DiskDrive" -Value $StorageProfile.Model -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "DiskManufacturer" -Value $StorageProfile.Manufacturer -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "DiskModel" -Value $StorageProfile.Model -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "DiskSerialNumber" -Value $StorageProfile.SerialNumber -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "DiskFirmware" -Value $StorageProfile.FirmwareVersion -Type "String"
                
                # Media and interface information
                Set-RegistryValue -Path $wmiPath -Name "MediaType" -Value "Fixed hard disk media" -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "InterfaceType" -Value $StorageProfile.Interface -Type "String"
                Set-RegistryValue -Path $wmiPath -Name "Partitions" -Value 4 -Type "DWord"
                
                # Capacity and performance
                Set-RegistryValue -Path $wmiPath -Name "Size" -Value ($StorageProfile.Capacity * 1024 * 1024 * 1024) -Type "QWord"
                Set-RegistryValue -Path $wmiPath -Name "BytesPerSector" -Value 512 -Type "DWord"
                Set-RegistryValue -Path $wmiPath -Name "SectorsPerTrack" -Value 63 -Type "DWord"
                Set-RegistryValue -Path $wmiPath -Name "TracksPerCylinder" -Value 255 -Type "DWord"
                
                $modifiedCount++
            }
        }
        
        Write-ModuleLog "Storage WMI spoofing completed: $modifiedCount paths modified" "Info"
        return @{ Success = $true; ModifiedCount = $modifiedCount }
    }
    catch {
        Write-ModuleLog "Storage WMI spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

#endregion

#region Storage Device Manager Spoofing

function Invoke-StorageDeviceManagerSpoofing {
    <#
    .SYNOPSIS
        Spoofs storage device entries in Device Manager
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting storage Device Manager spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Device Manager storage paths
        $devicePaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\IDE",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\STORAGE"
        )
        
        foreach ($devicePath in $devicePaths) {
            if (Test-Path $devicePath) {
                $subKeys = Get-ChildItem -Path $devicePath -Recurse -ErrorAction SilentlyContinue
                
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    
                    # Check if this is a storage device entry
                    if ($properties -and (
                        $properties.Class -eq "DiskDrive" -or
                        $fullPath -like "*Disk*" -or
                        $properties.DeviceDesc -like "*Disk*" -or
                        $properties.DeviceDesc -like "*Drive*"
                    )) {
                        
                        # Device identification
                        Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $StorageProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "FriendlyName" -Value $StorageProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "LocationInformation" -Value "Bus 0, Target 0, LUN 0" -Type "String"
                        
                        # Manufacturer and driver information
                        Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $StorageProfile.Manufacturer -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Provider" -Value $StorageProfile.Manufacturer -Type "String"
                        
                        # Storage-specific properties
                        Set-RegistryValue -Path $fullPath -Name "Service" -Value "disk" -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ConfigFlags" -Value 0 -Type "DWord"
                        Set-RegistryValue -Path $fullPath -Name "Capabilities" -Value 0x10 -Type "DWord"
                        
                        # Hardware identifiers
                        $hwIds = @(
                            "SCSI\Disk&Ven_$($StorageProfile.Manufacturer)&Prod_$($StorageProfile.Model.Replace(' ', '_'))",
                            "SCSI\Disk$($StorageProfile.Manufacturer)$($StorageProfile.Model.Replace(' ', '_'))",
                            "SCSI\$($StorageProfile.Manufacturer)$($StorageProfile.Model.Replace(' ', '_'))",
                            "GenDisk"
                        )
                        Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value $hwIds -Type "MultiString"
                        
                        # Compatible IDs
                        $compatIds = @(
                            "GenDisk",
                            "SCSI\DiskGeneric",
                            "SCSI\RAW"
                        )
                        Set-RegistryValue -Path $fullPath -Name "CompatibleIDs" -Value $compatIds -Type "MultiString"
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        Write-ModuleLog "Storage Device Manager spoofing completed: $modifiedCount devices modified" "Info"
        return @{ Success = $true; ModifiedCount = $modifiedCount }
    }
    catch {
        Write-ModuleLog "Storage Device Manager spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

#endregion

#region Main Storage Spoofing Function

function Invoke-StorageSpoofing {
    <#
    .SYNOPSIS
        Main function to execute comprehensive storage spoofing
    .DESCRIPTION
        Orchestrates all storage spoofing operations including drives, controllers, and SMART data
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$StorageProfile = $null,
        [string]$PreferredType = "SSD_NVMe",
        [string]$PreferredManufacturer = "Samsung"
    )
    
    Write-ModuleLog "Starting comprehensive storage spoofing..." "Info"
    
    try {
        # Generate or use provided storage profile
        if (-not $StorageProfile) {
            $StorageProfile = Get-RealisticStorageProfile -PreferredType $PreferredType -PreferredManufacturer $PreferredManufacturer
        }
        
        Write-ModuleLog "Using storage profile: $($StorageProfile.Model) ($($StorageProfile.Interface), $($StorageProfile.Capacity)GB)" "Info"
        
        $results = @()
        
        # Execute storage spoofing operations
        $results += Invoke-StorageRegistrySpoofing -StorageProfile $StorageProfile -Config $Config
        $results += Invoke-SMARTDataSpoofing -StorageProfile $StorageProfile -Config $Config
        $results += Invoke-StorageControllerSpoofing -StorageProfile $StorageProfile -Config $Config
        $results += Invoke-StorageWMISpoofing -StorageProfile $StorageProfile -Config $Config
        $results += Invoke-StorageDeviceManagerSpoofing -StorageProfile $StorageProfile -Config $Config
        
        # Calculate success metrics
        $successCount = ($results | Where-Object { $_.Success }).Count
        $totalOperations = $results.Count
        $totalModified = ($results | ForEach-Object { $_.ModifiedCount } | Measure-Object -Sum).Sum
        
        $success = $successCount -eq $totalOperations
        
        if ($success) {
            $message = "Storage spoofed to $($StorageProfile.Model) ($($StorageProfile.Interface), $($StorageProfile.Capacity)GB) - $totalModified entries modified"
            Write-ModuleLog $message "Info"
        } else {
            $message = "Storage spoofing partially failed: $successCount/$totalOperations operations successful"
            Write-ModuleLog $message "Warning"
        }
        
        return @{
            Success = $success
            Message = $message
            StorageProfile = $StorageProfile
            Results = $results
            TotalModified = $totalModified
        }
    }
    catch {
        $errorMessage = "Storage spoofing failed: $($_.Exception.Message)"
        Write-ModuleLog $errorMessage "Error"
        return @{
            Success = $false
            Message = $errorMessage
            Error = $_.Exception.Message
        }
    }
}

#endregion

#region Storage Validation Functions

function Test-StorageSpoofingEffectiveness {
    <#
    .SYNOPSIS
        Tests the effectiveness of storage spoofing by checking common detection points
    #>
    [CmdletBinding()]
    param()
    
    Write-ModuleLog "Testing storage spoofing effectiveness..." "Info"
    
    try {
        $tests = @()
        
        # Test 1: Check for VMware disk artifacts
        $vmwareDisks = $false
        $diskPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI\*",
            "HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum"
        )
        
        foreach ($diskPath in $diskPaths) {
            $keys = Get-ChildItem -Path $diskPath -ErrorAction SilentlyContinue
            if ($keys) {
                foreach ($key in $keys) {
                    $props = Get-ItemProperty -Path $key.PSPath -ErrorAction SilentlyContinue
                    if ($props -and ($props.DeviceDesc -like "*VMware*" -or $props.FriendlyName -like "*VMware*")) {
                        $vmwareDisks = $true
                        break
                    }
                }
            }
        }
        
        $tests += @{
            Test = "VMware Disk Artifact Detection"
            Result = if (-not $vmwareDisks) { "PASS" } else { "FAIL" }
            Details = if ($vmwareDisks) { "VMware disk artifacts found" } else { "No VMware disk artifacts detected" }
        }
        
        # Test 2: Check for realistic storage device
        $realisticStorage = $false
        $storagePaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI\*"
        )
        
        foreach ($storagePath in $storagePaths) {
            $keys = Get-ChildItem -Path $storagePath -ErrorAction SilentlyContinue
            if ($keys) {
                foreach ($key in $keys) {
                    $props = Get-ItemProperty -Path $key.PSPath -ErrorAction SilentlyContinue
                    if ($props -and (
                        $props.DeviceDesc -like "*Samsung*" -or 
                        $props.DeviceDesc -like "*WD*" -or 
                        $props.DeviceDesc -like "*Crucial*" -or
                        $props.DeviceDesc -like "*Seagate*"
                    )) {
                        $realisticStorage = $true
                        break
                    }
                }
            }
        }
        
        $tests += @{
            Test = "Realistic Storage Detection"
            Result = if ($realisticStorage) { "PASS" } else { "FAIL" }
            Details = if ($realisticStorage) { "Realistic storage device detected" } else { "No realistic storage device found" }
        }
        
        # Test 3: Check SMART data presence
        $smartDataFound = $false
        $smartPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Services\disk\SMART"
        )
        
        foreach ($smartPath in $smartPaths) {
            if (Test-Path $smartPath) {
                $smartKeys = Get-ChildItem -Path $smartPath -ErrorAction SilentlyContinue
                if ($smartKeys -and $smartKeys.Count -gt 0) {
                    $smartDataFound = $true
                    break
                }
            }
        }
        
        $tests += @{
            Test = "SMART Data Presence"
            Result = if ($smartDataFound) { "PASS" } else { "FAIL" }
            Details = if ($smartDataFound) { "SMART data found" } else { "No SMART data detected" }
        }
        
        # Calculate overall effectiveness
        $passedTests = ($tests | Where-Object { $_.Result -eq "PASS" }).Count
        $totalTests = $tests.Count
        $effectiveness = [math]::Round(($passedTests / $totalTests) * 100, 1)
        
        Write-ModuleLog "Storage spoofing effectiveness: $effectiveness% ($passedTests/$totalTests tests passed)" "Info"
        
        return @{
            Success = $true
            Effectiveness = $effectiveness
            PassedTests = $passedTests
            TotalTests = $totalTests
            TestResults = $tests
        }
    }
    catch {
        Write-ModuleLog "Storage spoofing effectiveness test failed: $($_.Exception.Message)" "Error"
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

#endregion

# Export functions
Export-ModuleMember -Function @(
    'Invoke-StorageSpoofing',
    'Get-RealisticStorageProfile',
    'New-StorageSerialNumber',
    'Get-RealisticSMARTData',
    'Invoke-StorageRegistrySpoofing',
    'Invoke-SMARTDataSpoofing',
    'Invoke-StorageControllerSpoofing',
    'Invoke-StorageWMISpoofing',
    'Invoke-StorageDeviceManagerSpoofing',
    'Test-StorageSpoofingEffectiveness'
)
