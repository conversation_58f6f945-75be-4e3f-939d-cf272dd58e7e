@{
    RootModule = 'FileSystemOperations.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'd2f8c4a1-7e9b-4c3f-8a6d-9b5c2f7e4a3d'
    Author = 'Anti-VM Detection System'
    Description = 'FileSystem operations module for file cleanup and driver replacement'
    
    FunctionsToExport = @(
        'Invoke-FileSystemOperations',
        'Invoke-FileCleanup',
        'Invoke-DriverReplacement',
        'Invoke-VMartifactCleanup'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}
