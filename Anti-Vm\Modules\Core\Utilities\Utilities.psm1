# Utilities Module - Simplified Version
function Get-RandomGUID {
    return [System.Guid]::NewGuid().ToString()
}

function Get-RandomSerial {
    param([string]$Manufacturer = "Default")
    $random = Get-Random -Minimum 100000000 -Maximum 999999999
    return "$($Manufacturer[0..2] -join '')$random"
}

function Get-RandomMACAddress {
    $bytes = 1..6 | ForEach-Object { Get-Random -Minimum 0 -Maximum 255 }
    return ($bytes | ForEach-Object { $_.ToString("X2") }) -join "-"
}

function Set-RegistryValue {
    param(
        [string]$Path,
        [string]$Name,
        [object]$Value,
        [string]$Type
    )
    
    try {
        if (-not (Test-Path $Path)) {
            New-Item -Path $Path -Force | Out-Null
        }
        Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type $Type -Force
    }
    catch {
        Write-ModuleLog "Failed to set registry value $Path\$Name : $($_.Exception.Message)" "Warning"
    }
}

function Get-RandomDriverContent {
    param([string]$DriverName)
    # Return dummy driver content (simplified)
    $dummyBytes = 1..1024 | ForEach-Object { Get-Random -Minimum 0 -Maximum 255 }
    return [byte[]]$dummyBytes
}

Export-ModuleMember -Function @('Get-RandomGUID', 'Get-RandomSerial', 'Get-RandomMACAddress', 'Set-RegistryValue', 'Get-RandomDriverContent')
