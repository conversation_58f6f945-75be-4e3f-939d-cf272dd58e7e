{"metadata": {"version": "2.0", "description": "Anti-VM Detection Toolkit Configuration", "author": "Cybersecurity Research Team", "lastModified": "2025-01-01", "targetMalware": ["LockBit 3.0", "BlackCat/ALPHV", "Royal Ransomware", "Emotet", "<PERSON><PERSON><PERSON>", "IcedID", "Cobalt Strike", "APT29 (<PERSON><PERSON>)", "APT40 (<PERSON><PERSON><PERSON>)", "Lazarus Group"]}, "modules": {"hardwareFingerprinting": {"enabled": true, "description": "Spoofs hardware characteristics to bypass fingerprinting", "cpuSpoofing": {"enabled": true, "description": "Modifies CPU identification and features", "riskLevel": "Medium", "techniques": ["CPUID manipulation", "Registry modification", "WMI spoofing"]}, "gpuMasking": {"enabled": true, "description": "Masks VMware SVGA adapters as realistic GPUs", "riskLevel": "Low", "techniques": ["Device Manager hiding", "Registry modification", "Driver replacement"]}, "storageSimulation": {"enabled": true, "description": "Replaces virtual disk identifiers with realistic models", "riskLevel": "Medium", "techniques": ["Device enumeration spoofing", "SMART data simulation", "Serial number generation"]}, "memoryProfileModification": {"enabled": true, "description": "Spoofs RAM manufacturer and specifications", "riskLevel": "Low", "techniques": ["SPD data modification", "Registry spoofing", "WMI manipulation"]}, "motherboardReplacement": {"enabled": true, "description": "Changes motherboard and BIOS information", "riskLevel": "High", "techniques": ["SMBIOS modification", "DMI spoofing", "ACPI table manipulation"]}, "networkAdapterSpoofing": {"enabled": true, "description": "Replaces VMware network adapters with Intel equivalents", "riskLevel": "Medium", "techniques": ["MAC address randomization", "OUI spoofing", "Driver replacement"]}}, "systemArtifactsCleanup": {"enabled": true, "description": "Removes or masks VMware-specific system artifacts", "registrySanitization": {"enabled": true, "description": "Removes VMware registry keys and entries", "riskLevel": "High", "backupRequired": true, "targetKeys": ["HKLM\\SOFTWARE\\VMware, Inc.", "HKLM\\SYSTEM\\CurrentControlSet\\Services\\VMTools", "HKLM\\HARDWARE\\DESCRIPTION\\System"]}, "filesystemCleanup": {"enabled": true, "description": "Removes or renames VMware Tools files and drivers", "riskLevel": "High", "backupRequired": true, "targetPaths": ["C:\\Program Files\\VMware", "C:\\Windows\\System32\\drivers\\vm*.sys", "C:\\Windows\\System32\\vmGuestLib.dll"]}, "processObfuscation": {"enabled": true, "description": "Hides or modifies VMware processes and services", "riskLevel": "Medium", "targetProcesses": ["vmtoolsd", "vmwaretray", "vmwareuser"], "targetServices": ["VMTools", "vmci", "VGAuthService"]}, "deviceDriverMasking": {"enabled": true, "description": "Masks VMware device drivers", "riskLevel": "High", "targetDrivers": ["vmci", "vmhgfs", "vmmouse", "vmrawdsk", "vm3dmp"]}, "biosSpoof": {"enabled": true, "description": "Modifies BIOS and UEFI information", "riskLevel": "High", "techniques": ["ACPI table modification", "SMBIOS spoofing", "DMI manipulation"]}}, "behavioralEvasion": {"enabled": true, "description": "Normalizes VM behavior to match physical hardware", "performanceNormalization": {"enabled": true, "description": "Adjusts timing and performance characteristics", "riskLevel": "Low", "techniques": ["Performance counter adjustment", "Timing normalization"]}, "wmiInterception": {"enabled": true, "description": "Intercepts and modifies WMI query responses", "riskLevel": "High", "implementationNote": "Requires WMI provider hooks for full effectiveness", "targetClasses": ["Win32_ComputerSystem", "Win32_BaseBoard", "Win32_BIOS", "Win32_Processor", "Win32_VideoController"]}, "humanInteractionSimulation": {"enabled": true, "description": "Creates artifacts of human user activity", "riskLevel": "Low", "techniques": ["Registry entries", "File access patterns", "Browser history"]}, "hardwareEnumerationSpoofing": {"enabled": true, "description": "Simulates presence of physical peripherals", "riskLevel": "Low", "devices": ["USB mouse", "USB keyboard", "Audio hardware", "Webcam"]}, "networkBehaviorMasking": {"enabled": true, "description": "Masks virtual network behavior patterns", "riskLevel": "Low", "techniques": ["Latency simulation", "Bandwidth characteristics", "Protocol timing"]}}, "advancedBypass": {"enabled": true, "description": "Advanced techniques for sophisticated malware", "hypervisorCountermeasures": {"enabled": true, "description": "Bypasses hypervisor detection techniques", "riskLevel": "High", "techniques": ["CPUID leaf masking", "MSR hiding", "Hypervisor bit clearing"], "targetDetectionMethods": ["CPUID.1:ECX[31] hypervisor bit", "CPUID.0x40000000 VMware leaf", "MSR access patterns", "VM exit timing"]}, "memorySignatureCleanup": {"enabled": true, "description": "Removes VMware memory signatures", "riskLevel": "High", "implementationNote": "Requires kernel-level memory patching for full effect", "techniques": ["Memory pattern masking", "Allocation signature removal"]}, "eventLogSanitization": {"enabled": true, "description": "Removes VMware installation and operation events", "riskLevel": "Medium", "targetLogs": ["System", "Application", "Setup", "Security"], "destructive": true}, "environmentalSimulation": {"enabled": true, "description": "Simulates physical hardware environmental sensors", "riskLevel": "Low", "features": ["Temperature sensors", "Power management", "Battery status", "Fan controls"]}, "firmwareTableModification": {"enabled": true, "description": "Modifies firmware tables to hide virtualization", "riskLevel": "High", "tables": ["ACPI DSDT", "ACPI SSDT", "SMBIOS", "DMI"], "requiresKernelAccess": true}}}, "hardware": {"cpu": {"vendor": "GenuineIntel", "brand": "Intel(R) Core(TM) i7-12700K CPU @ 3.60GHz", "family": 6, "model": 151, "stepping": 2, "cores": 8, "threads": 16, "frequency": 3600, "cache": {"l1": "80KB per core", "l2": "1.25MB per core", "l3": "25MB shared"}, "features": {"avx": true, "avx2": true, "aes": true, "virtualization": false}}, "gpu": {"vendor": "NVIDIA Corporation", "device": "NVIDIA GeForce RTX 4070", "vendorId": "0x10DE", "deviceId": "0x2786", "vram": "12GB", "driver": "531.18", "computeCapability": "8.9"}, "motherboard": {"manufacturer": "ASUS", "product": "ROG STRIX Z690-E GAMING WIFI", "version": "Rev 1.02", "chipset": "Intel Z690", "socket": "LGA1700", "formFactor": "ATX"}, "memory": {"totalSize": "32GB", "manufacturer": "Corsair", "partNumber": "CMK32GX4M2E3200C16", "type": "DDR4", "speed": "3200MHz", "timing": "16-18-18-36", "modules": 2, "moduleSize": "16GB"}, "storage": {"primary": {"model": "Samsung SSD 980 PRO 1TB", "interface": "NVMe PCIe 4.0", "capacity": "1TB", "firmware": "5B2QGXA7", "manufacturer": "Samsung"}}, "network": {"adapter": "Intel(R) Ethernet Connection I225-V", "speed": "1Gbps", "macOUI": "00:1B:21", "driver": "Intel PROSet"}, "audio": {"device": "Realtek ALC4080", "channels": "7.1", "sampleRate": "192kHz", "bitDepth": "32-bit"}}, "safety": {"createBackups": true, "backupLocation": ".\\Backups", "performStabilityChecks": true, "requireConfirmation": false, "maxRiskLevel": "High", "rollbackOnFailure": true, "validateChanges": true}, "logging": {"level": "Info", "fileLogging": true, "consoleOutput": true, "maxLogSize": "10MB", "retention": "30 days", "sensitiveDataMasking": true}, "compatibility": {"windowsVersions": ["Windows 10", "Windows 11"], "vmwareVersions": ["Workstation 16.x", "Workstation 17.x"], "powerShellVersions": ["5.1", "7.x"], "requiredPrivileges": ["Administrator"], "testedEnvironments": ["VMware Workstation 17.0.2 on Windows 11 22H2", "VMware Workstation 16.2.4 on Windows 10 21H2"]}, "performance": {"estimatedExecutionTime": "2-5 minutes", "systemRestartRequired": true, "performanceImpact": "Minimal", "memoryUsage": "Low", "diskSpace": "100MB for backups"}, "research": {"effectivenessRating": {"basicMalware": "95%", "advancedMalware": "85%", "aptGroups": "75%", "customDetection": "60%"}, "detectionMethods": {"bypassed": ["Registry artifact detection", "Process enumeration", "Hardware fingerprinting", "WMI queries", "CPUID analysis", "MAC address analysis", "Device driver enumeration", "Service detection", "File system artifacts", "Performance timing", "Memory allocation patterns", "Event log analysis"], "partiallyBypassed": ["Advanced memory forensics", "Kernel-level hypervisor detection", "Custom CPUID sequences", "Hardware-specific timing attacks"], "limitations": ["Deep kernel-level detection requires additional tools", "Some advanced timing attacks may still be detectable", "Custom malware with unique detection methods may bypass"]}}, "deployment": {"preRequisites": ["Isolated research environment", "Full system backup or VM snapshot", "Administrative privileges", "Network isolation recommended", "Monitoring tools for stability"], "executionOrder": ["1. Create VM backup/snapshot", "2. <PERSON>-Configurator.ps1 on host", "3. Start VM and verify boot", "4. Run Anti-VMDetection.ps1 inside VM", "5. <PERSON><PERSON> VM for full effect", "6. Test with detection tools", "7. Deploy malware samples"], "rollbackProcedure": ["1. Stop all analysis activities", "2. Run Anti-VMDetection.ps1 -RollbackMode", "3. <PERSON><PERSON> V<PERSON> backup if needed", "4. <PERSON><PERSON> V<PERSON>", "5. Verify VMware Tools functionality"]}}