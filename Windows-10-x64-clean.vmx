.encoding = "windows-1252"
displayName = "Windows 10 x64"
config.version = "8"
virtualHW.version = "21"
mks.enable3d = "TRUE"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
vmci0.id = "1358565225"
hpet0.present = "TRUE"
nvram = "Windows 10 x64.nvram"
virtualHW.productCompatibility = "hosted"
powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"
firmware = "efi"
sensor.location = "pass-through"
guestOS = "windows9-64"
tools.syncTime = "FALSE"
tools.autostart = "FALSE"
tools.guest.desktop.autolock = "FALSE"
tools.guestlib.enableHostInfo = "FALSE"
tools.capability.verifiedSamlToken = "TRUE"
tools.remindInstall = "FALSE"
toolsInstallManager.updateCounter = "1"
toolsInstallManager.lastInstallError = "0"
tools.upgrade.policy = "useGlobal"
sound.autoDetect = "TRUE"
sound.virtualDev = "hdaudio"
sound.fileName = "-1"
sound.present = "TRUE"
sound.pciSlotNumber = "33"
numvcpus = "4"
cpuid.coresPerSocket = "4"
memsize = "8192"
mem.hotadd = "TRUE"
sata0.present = "TRUE"
sata0.pciSlotNumber = "35"
sata0:1.deviceType = "cdrom-raw"
sata0:1.fileName = "auto detect"
sata0:1.present = "TRUE"
sata0:1.autodetect = "TRUE"
sata0:1.clientDevice = "FALSE"
nvme0.present = "TRUE"
nvme0.pciSlotNumber = "224"
nvme0:0.fileName = "Windows 10 x64-000002.vmdk"
nvme0:0.present = "TRUE"
nvme0:0.redo = ""
usb.present = "TRUE"
usb.pciSlotNumber = "32"
ehci.present = "TRUE"
ehci.pciSlotNumber = "34"
usb_xhci.present = "TRUE"
usb_xhci.pciSlotNumber = "192"
usb_xhci:4.present = "TRUE"
usb_xhci:4.deviceType = "hid"
usb_xhci:4.port = "4"
usb_xhci:4.parent = "-1"
svga.graphicsMemoryKB = "8388608"
svga.vramSize = "268435456"
svga.guestBackedPrimaryAware = "TRUE"
ethernet0.addressType = "static"
ethernet0.virtualDev = "e1000e"
ethernet0.address = "00:1B:21:8A:4F:2C"
ethernet0.present = "TRUE"
ethernet0.generatedAddress = "00:1B:21:8A:4F:2C"
ethernet0.generatedAddressOffset = "0"
ethernet0.pciSlotNumber = "160"
ethernet0.checkMACAddress = "FALSE"
ethernet0.connectionType = "nat"
ethernet0.wakeonpcktrcv = "FALSE"
floppy0.fileType = "device"
floppy0.fileName = ""
floppy0.clientDevice = "FALSE"
floppy0.autodetect = "TRUE"
floppy0.present = "FALSE"
extendedConfigFile = "Windows 10 x64.vmxf"
vmxstats.filename = "Windows 10 x64.scoreboard"
numa.autosize.cookie = "40042"
numa.autosize.vcpu.maxPerVirtualNode = "4"
uuid.bios = "56 4d 83 ce 89 3d c9 de-e6 7d 0c 4c 50 fa 0f 69"
uuid.location = "56 4d 83 ce 89 3d c9 de-e6 7d 0c 4c 50 fa 0f 69"
pciBridge0.pciSlotNumber = "17"
pciBridge4.pciSlotNumber = "21"
pciBridge5.pciSlotNumber = "22"
pciBridge6.pciSlotNumber = "23"
pciBridge7.pciSlotNumber = "24"
vmotion.checkpointFBSize = "4194304"
vmotion.checkpointSVGAPrimarySize = "268435456"
vmotion.svga.mobMaxSize = "1073741824"
vmotion.svga.graphicsMemoryKB = "8388608"
vmotion.svga.supports3D = "1"
vmotion.svga.baseCapsLevel = "9"
vmotion.svga.maxPointSize = "189"
vmotion.svga.maxTextureSize = "32768"
vmotion.svga.maxVolumeExtent = "16384"
vmotion.svga.maxTextureAnisotropy = "16"
vmotion.svga.lineStipple = "1"
vmotion.svga.dxMaxConstantBuffers = "15"
vmotion.svga.dxProvokingVertex = "1"
vmotion.svga.sm41 = "1"
vmotion.svga.multisample2x = "1"
vmotion.svga.multisample4x = "1"
vmotion.svga.msFullQuality = "1"
vmotion.svga.logicOps = "1"
vmotion.svga.bc67 = "9"
vmotion.svga.sm5 = "1"
vmotion.svga.multisample8x = "1"
vmotion.svga.logicBlendOps = "1"
vmotion.svga.maxForcedSampleCount = "16"
vmotion.svga.gl43 = "1"
monitor.phys_bits_used = "45"
cleanShutdown = "TRUE"
softPowerOff = "TRUE"
vm.genid = "-547526383127770848"
vm.genidX = "-2148790432800805431"
guestInfo.detailed.data = "architecture='X86' bitness='64' buildNumber='19045' distroName='Windows' distroVersion='10.0' familyName='Windows' kernelVersion='19045.6216' prettyName='Windows 10 Pro, 64-bit (Build 19045.6216)'"

# CPUID Masking to hide hypervisor presence (Intel i7-12700K)
cpuid.0.eax = "0000000D"
cpuid.0.ebx = "756E6547"
cpuid.0.ecx = "6C65746E"
cpuid.0.edx = "49656E69"
cpuid.1.eax = "000906EA"
cpuid.1.ebx = "08100800"
cpuid.1.ecx = "7FFAFBBF"
cpuid.1.edx = "BFEBFBFF"
cpuid.80000000.eax = "80000008"
cpuid.80000001.eax = "00000000"
cpuid.80000001.ebx = "00000000"
cpuid.80000001.ecx = "00000121"
cpuid.80000001.edx = "2C100800"

# SMBIOS Information Spoofing (Dell OptiPlex)
smbios.reflecthost = "FALSE"
hw.model = "OptiPlex 7090"
hw.model.reflecthost = "FALSE"
board-id = "440BX Desktop Reference Platform"
board-id.reflecthost = "FALSE"

# Hide VMware-specific features
isolation.tools.copy.disable = "TRUE"
isolation.tools.paste.disable = "TRUE"
isolation.tools.hgfs.disable = "TRUE"
isolation.tools.unity.disable = "TRUE"
isolation.tools.vmxDnD.disable = "TRUE"
isolation.device.connectable.disable = "TRUE"
isolation.device.edit.disable = "TRUE"

# Hide hypervisor identification
monitor_control.restrict_backdoor = "TRUE"
monitor_control.disable_chksimd = "TRUE"
monitor_control.disable_ntreloc = "TRUE"
monitor_control.disable_selfmod = "TRUE"

# Advanced cloaking options
hypervisor.cpuid.v0 = "FALSE"
mce.enable = "TRUE"
vhv.enable = "FALSE"
