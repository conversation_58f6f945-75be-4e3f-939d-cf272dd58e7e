@{
    RootModule = 'SystemSpoofing.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'e7f3a9c2-5d8b-4f1e-9a7c-3b6f2d8e5a1c'
    Author = 'Anti-VM Detection System'
    Description = 'System-level spoofing module for BIOS, Drivers, Services, and Processes'
    
    FunctionsToExport = @(
        'Invoke-SystemSpoofing',
        'Invoke-BIOSSpoofing',
        'Invoke-DriverSpoofing',
        'Invoke-ServiceSpoofing',
        'Invoke-ProcessSpoofing'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}
