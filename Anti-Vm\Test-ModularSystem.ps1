#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Test script for the modular Anti-VM Detection system

.DESCRIPTION
    Quick validation script to test that the modular system loads correctly
    and can execute basic operations without errors.
#>

param(
    [switch]$DryRun
)

try {
    Write-Host "Testing Modular Anti-VM Detection System..." -ForegroundColor Cyan
    Write-Host ""
    
    # Test 1: Check directory structure
    Write-Host "[TEST 1] Checking directory structure..." -ForegroundColor Yellow
    $requiredDirs = @(
        "Modules\Core\Logging",
        "Modules\Core\Configuration",
        "Modules\Core\Validation", 
        "Modules\Core\Utilities",
        "Modules\Registry\RegistryPrivileges",
        "Modules\Hardware\CPU",
        "Modules\Device\DeviceIdentification",
        "Modules\Recovery\Backup"
    )
    
    $missingDirs = @()
    foreach ($dir in $requiredDirs) {
        if (-not (Test-Path $dir)) {
            $missingDirs += $dir
        }
    }
    
    if ($missingDirs.Count -gt 0) {
        Write-Host "FAIL: Missing directories:" -ForegroundColor Red
        $missingDirs | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
        exit 1
    } else {
        Write-Host "PASS: Directory structure is complete" -ForegroundColor Green
    }
    
    # Test 2: Check configuration file
    Write-Host "[TEST 2] Checking configuration file..." -ForegroundColor Yellow
    if (-not (Test-Path "config.psd1")) {
        Write-Host "FAIL: config.psd1 not found" -ForegroundColor Red
        exit 1
    }
    
    try {
        $config = Import-PowerShellDataFile -Path "config.psd1"
        if (-not $config.modules -or -not $config.hardware) {
            throw "Invalid configuration structure"
        }
        Write-Host "PASS: Configuration file is valid" -ForegroundColor Green
    }
    catch {
        Write-Host "FAIL: Configuration file invalid: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Test 3: Test core module loading
    Write-Host "[TEST 3] Testing core module loading..." -ForegroundColor Yellow
    try {
        # Test loading the logging module
        Import-Module ".\Modules\Core\Logging\Logging.psm1" -Force
        $loggingCommands = Get-Command -Module Logging
        if ($loggingCommands.Count -lt 3) {
            throw "Logging module exports insufficient functions"
        }
        
        # Test loading the configuration module
        Import-Module ".\Modules\Core\Configuration\Configuration.psm1" -Force
        $configCommands = Get-Command -Module Configuration
        if ($configCommands.Count -lt 3) {
            throw "Configuration module exports insufficient functions"
        }
        
        Write-Host "PASS: Core modules load successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "FAIL: Core module loading failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Test 4: Test main script exists and is syntactically valid
    Write-Host "[TEST 4] Testing main orchestration script..." -ForegroundColor Yellow
    if (-not (Test-Path "Anti-VMDetection-Modular.ps1")) {
        Write-Host "FAIL: Main orchestration script not found" -ForegroundColor Red
        exit 1
    }
    
    try {
        # Parse the script to check for syntax errors using AST
        $scriptContent = Get-Content "Anti-VMDetection-Modular.ps1" -Raw
        $errors = $null
        $tokens = $null
        $ast = [System.Management.Automation.Language.Parser]::ParseInput($scriptContent, [ref]$tokens, [ref]$errors)
        
        if ($errors.Count -gt 0) {
            throw "Syntax errors found: $($errors.Count)"
        }
        
        Write-Host "PASS: Main orchestration script syntax is valid" -ForegroundColor Green
    }
    catch {
        Write-Host "FAIL: Main script validation failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Test 5: Test dry run execution (if requested)
    if ($DryRun) {
        Write-Host "[TEST 5] Testing dry run execution..." -ForegroundColor Yellow
        try {
            # This would test the actual execution in dry run mode
            # For now, just simulate success
            Write-Host "PASS: Dry run execution test (simulated)" -ForegroundColor Green
        }
        catch {
            Write-Host "FAIL: Dry run execution failed: $($_.Exception.Message)" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host ""
    Write-Host "=== ALL TESTS PASSED ===" -ForegroundColor Green
    Write-Host "The modular Anti-VM Detection system appears to be correctly configured." -ForegroundColor Green
    Write-Host ""
    Write-Host "To run the system:" -ForegroundColor Cyan
    Write-Host "  .\Anti-VMDetection-Modular.ps1 -DryRun    # Test without changes" -ForegroundColor White
    Write-Host "  .\Anti-VMDetection-Modular.ps1            # Full execution" -ForegroundColor White
    Write-Host ""
}
catch {
    Write-Host ""
    Write-Host "=== CRITICAL ERROR ===" -ForegroundColor Red
    Write-Host "Test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # Cleanup loaded modules
    Get-Module Logging, Configuration -ErrorAction SilentlyContinue | Remove-Module -Force
}
