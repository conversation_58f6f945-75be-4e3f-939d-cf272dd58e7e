# Test Script for Enhanced Anti-VM Detection System
# Tests the comprehensive hardware spoofing and VM detection bypass capabilities
# Requires Administrator privileges

param(
    [switch]$Verbose = $false,
    [switch]$TestOnly = $false,
    [string]$ConfigPath = ".\config.json"
)

# Set error action and verbose preference
$ErrorActionPreference = "Stop"
if ($Verbose) { $VerbosePreference = "Continue" }

# Test if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

Write-Host "=== Enhanced Anti-VM Detection System Test ===" -ForegroundColor Cyan
Write-Host "Testing comprehensive hardware spoofing capabilities..." -ForegroundColor Green
Write-Host ""

try {
    # Load configuration
    Write-Host "[1/8] Loading configuration..." -ForegroundColor Yellow
    if (-not (Test-Path $ConfigPath)) {
        throw "Configuration file not found: $ConfigPath"
    }
    
    $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
    Write-Host "✓ Configuration loaded successfully" -ForegroundColor Green
    Write-Host "  - Version: $($config.metadata.version)" -ForegroundColor Gray
    Write-Host "  - Description: $($config.metadata.description)" -ForegroundColor Gray
    Write-Host ""

    # Import Core Modules
    Write-Host "[2/8] Loading core modules..." -ForegroundColor Yellow
    
    $moduleImports = @(
        ".\Modules\Core\Logging\Logging.psm1",
        ".\Modules\Core\Utilities\Utilities.psm1", 
        ".\Modules\Registry\RegistryPrivileges\RegistryPrivileges.psm1"
    )
    
    foreach ($module in $moduleImports) {
        if (Test-Path $module) {
            Import-Module $module -Force
            Write-Host "✓ Imported: $module" -ForegroundColor Green
        } else {
            Write-Warning "Module not found: $module"
        }
    }
    Write-Host ""

    # Import Hardware Spoofing Module
    Write-Host "[3/8] Loading hardware spoofing module..." -ForegroundColor Yellow
    $hardwareModule = ".\Modules\Hardware\HardwareSpoofing.psm1"
    if (Test-Path $hardwareModule) {
        Import-Module $hardwareModule -Force
        Write-Host "✓ Hardware spoofing module loaded successfully" -ForegroundColor Green
        
        # Test hardware profile generation
        Write-Host "  Testing hardware profile generation..." -ForegroundColor Gray
        $testProfile = Get-RealisticHardwareProfile
        Write-Host "  ✓ Generated profile: $($testProfile.CPU.Name)" -ForegroundColor Gray
        Write-Host "  ✓ GPU: $($testProfile.GPU.Model)" -ForegroundColor Gray
        Write-Host "  ✓ Motherboard: $($testProfile.Motherboard.Manufacturer) $($testProfile.Motherboard.Model)" -ForegroundColor Gray
    } else {
        throw "Hardware spoofing module not found: $hardwareModule"
    }
    Write-Host ""

    # Test Registry Privileges
    Write-Host "[4/8] Testing registry access..." -ForegroundColor Yellow
    try {
        Enable-RegistryPrivileges
        Write-Host "✓ Registry privileges enabled successfully" -ForegroundColor Green
        
        # Test registry access to common hardware keys
        $testKeys = @(
            "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
        )
        
        foreach ($key in $testKeys) {
            if (Test-Path $key) {
                Write-Host "  ✓ Access verified: $key" -ForegroundColor Gray
            } else {
                Write-Host "  ! Key not found: $key" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Warning "Registry privilege elevation failed: $($_.Exception.Message)"
    }
    Write-Host ""

    # Initialize logging system
    Write-Host "[5/8] Initializing logging system..." -ForegroundColor Yellow
    try {
        Initialize-ModuleLogging -LogLevel "Info" -LogToFile $true
        Write-ModuleLog "Enhanced Anti-VM Detection System test started" "Info"
        Write-Host "✓ Logging system initialized" -ForegroundColor Green
    } catch {
        Write-Warning "Logging initialization failed: $($_.Exception.Message)"
    }
    Write-Host ""

    # Test VM Detection (Pre-spoofing)
    Write-Host "[6/8] Testing VM detection (before spoofing)..." -ForegroundColor Yellow
    try {
        # Test basic detection methods
        $vmDetected = $false
        
        # Check for VMware processes
        $vmwareProcesses = Get-Process | Where-Object { $_.ProcessName -like "*vmware*" -or $_.ProcessName -like "*vm*" }
        if ($vmwareProcesses) {
            Write-Host "  ! VMware processes detected: $($vmwareProcesses.Count)" -ForegroundColor Red
            $vmDetected = $true
        }
        
        # Check for VMware services
        $vmwareServices = Get-Service | Where-Object { $_.Name -like "*vmware*" -or $_.Name -like "*vm*" }
        if ($vmwareServices) {
            Write-Host "  ! VMware services detected: $($vmwareServices.Count)" -ForegroundColor Red
            $vmDetected = $true
        }
        
        # Check for VM-specific registry keys
        $vmRegistryKeys = @(
            "HKLM:\SOFTWARE\VMware, Inc.",
            "HKLM:\SYSTEM\CurrentControlSet\Services\VMTools"
        )
        
        foreach ($regKey in $vmRegistryKeys) {
            if (Test-Path $regKey) {
                Write-Host "  ! VM registry key found: $regKey" -ForegroundColor Red
                $vmDetected = $true
            }
        }
        
        if ($vmDetected) {
            Write-Host "  Result: VM environment detected" -ForegroundColor Red
        } else {
            Write-Host "  Result: No VM signatures found" -ForegroundColor Green
        }
    } catch {
        Write-Warning "VM detection test failed: $($_.Exception.Message)"
    }
    Write-Host ""

    if (-not $TestOnly) {
        # Execute Hardware Spoofing
        Write-Host "[7/8] Executing comprehensive hardware spoofing..." -ForegroundColor Yellow
        try {
            # Convert JSON config to PowerShell hashtable format for module compatibility
            $moduleConfig = @{
                Modules = @{
                    Hardware = @{
                        CPU = @{ Enabled = $config.modules.hardware.CPU.enabled }
                        GPU = @{ Enabled = $config.modules.hardware.GPU.enabled }
                        Storage = @{ Enabled = $config.modules.hardware.Storage.enabled }
                        Memory = @{ Enabled = $config.modules.hardware.Memory.enabled }
                        Motherboard = @{ Enabled = $config.modules.hardware.Motherboard.enabled }
                        Network = @{ Enabled = $config.modules.hardware.Network.enabled }
                        Audio = @{ Enabled = $config.modules.hardware.Audio.enabled }
                        USB = @{ Enabled = $config.modules.hardware.USB.enabled }
                        SMBIOS = @{ Enabled = $config.modules.hardware.SMBIOS.enabled }
                        Sensors = @{ Enabled = $config.modules.hardware.Sensors.enabled }
                    }
                }
                HardwareSpecs = @{
                    Memory = @{
                        TotalSize = 34359738368  # 32GB in bytes
                        Speed = 3200
                        Type = "DDR4"
                    }
                    Network = @{
                        AdapterName = $config.hardware.network.adapter
                        MACAddress = "00-1B-21-$(Get-Random -Min 10 -Max 99)-$(Get-Random -Min 10 -Max 99)-$(Get-Random -Min 10 -Max 99)"
                    }
                }
            }
            
            $spoofingResult = Invoke-HardwareSpoofing -Config $moduleConfig
            
            if ($spoofingResult.Success) {
                Write-Host "✓ Hardware spoofing completed successfully!" -ForegroundColor Green
                Write-Host "  - Modules successful: $($spoofingResult.Results.Count)" -ForegroundColor Gray
                Write-Host "  - Generated profile: $($spoofingResult.HardwareProfile.CPU.Name)" -ForegroundColor Gray
                
                # Display spoofing results
                foreach ($result in $spoofingResult.Results) {
                    if ($result.Success) {
                        Write-Host "    ✓ $($result.Message)" -ForegroundColor Green
                    } else {
                        Write-Host "    ✗ $($result.Message)" -ForegroundColor Red
                    }
                }
            } else {
                Write-Host "✗ Hardware spoofing failed!" -ForegroundColor Red
                foreach ($result in $spoofingResult.Results) {
                    if (-not $result.Success) {
                        Write-Host "    ✗ $($result.Message)" -ForegroundColor Red
                    }
                }
            }
        } catch {
            Write-Host "✗ Hardware spoofing execution failed: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
        }
        Write-Host ""
    } else {
        Write-Host "[7/8] Skipping hardware spoofing (TestOnly mode)" -ForegroundColor Yellow
        Write-Host ""
    }

    # Final Validation
    Write-Host "[8/8] Running final validation..." -ForegroundColor Yellow
    try {
        # Test if key spoofing targets exist and were modified
        $validationTests = @()
        
        # CPU validation
        $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
        if (Test-Path $cpuPath) {
            $cpuName = Get-ItemProperty -Path $cpuPath -Name "ProcessorNameString" -ErrorAction SilentlyContinue
            if ($cpuName -and $cpuName.ProcessorNameString -notlike "*VM*" -and $cpuName.ProcessorNameString -notlike "*Virtual*") {
                $validationTests += @{ Component = "CPU"; Status = "PASS"; Value = $cpuName.ProcessorNameString }
            } else {
                $validationTests += @{ Component = "CPU"; Status = "FAIL"; Value = "Virtual CPU detected" }
            }
        }
        
        # BIOS validation
        $biosPath = "HKLM:\HARDWARE\DESCRIPTION\System\BIOS"
        if (Test-Path $biosPath) {
            $biosVendor = Get-ItemProperty -Path $biosPath -Name "BIOSVendor" -ErrorAction SilentlyContinue
            if ($biosVendor -and $biosVendor.BIOSVendor -notlike "*VMware*" -and $biosVendor.BIOSVendor -notlike "*Virtual*") {
                $validationTests += @{ Component = "BIOS"; Status = "PASS"; Value = $biosVendor.BIOSVendor }
            } else {
                $validationTests += @{ Component = "BIOS"; Status = "FAIL"; Value = "Virtual BIOS detected" }
            }
        }
        
        # Display validation results
        foreach ($test in $validationTests) {
            if ($test.Status -eq "PASS") {
                Write-Host "  ✓ $($test.Component): $($test.Value)" -ForegroundColor Green
            } else {
                Write-Host "  ✗ $($test.Component): $($test.Value)" -ForegroundColor Red
            }
        }
        
        $passedTests = ($validationTests | Where-Object { $_.Status -eq "PASS" }).Count
        $totalTests = $validationTests.Count
        
        Write-Host ""
        Write-Host "Validation Summary: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })
        
    } catch {
        Write-Warning "Validation failed: $($_.Exception.Message)"
    }
    Write-Host ""

    # Final Summary
    Write-Host "=== Test Summary ===" -ForegroundColor Cyan
    Write-Host "✓ Configuration loaded and validated" -ForegroundColor Green
    Write-Host "✓ All core modules imported successfully" -ForegroundColor Green
    Write-Host "✓ Enhanced hardware spoofing module loaded" -ForegroundColor Green
    Write-Host "✓ Registry privileges enabled" -ForegroundColor Green
    Write-Host "✓ Logging system operational" -ForegroundColor Green
    
    if (-not $TestOnly) {
        Write-Host "✓ Hardware spoofing executed" -ForegroundColor Green
        Write-Host ""
        Write-Host "IMPORTANT: A system restart is recommended for full effect!" -ForegroundColor Yellow
        Write-Host "After restart, your VM will appear as a realistic physical system with:" -ForegroundColor White
        Write-Host "  - Authentic CPU with proper microcode and features" -ForegroundColor Gray
        Write-Host "  - Realistic GPU with proper PCI IDs and drivers" -ForegroundColor Gray
        Write-Host "  - Storage devices with SMART data and firmware versions" -ForegroundColor Gray
        Write-Host "  - Audio devices with proper codec information" -ForegroundColor Gray
        Write-Host "  - USB controllers and peripheral devices" -ForegroundColor Gray
        Write-Host "  - Complete SMBIOS/DMI table spoofing" -ForegroundColor Gray
        Write-Host "  - Hardware sensor data (temperature, voltage, fans)" -ForegroundColor Gray
    } else {
        Write-Host "○ Hardware spoofing test completed (dry run)" -ForegroundColor Blue
    }
    
    Write-Host ""
    Write-Host "Enhanced Anti-VM Detection System test completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "=== Test Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Gray
    Write-Host "Command: $($_.InvocationInfo.Line.Trim())" -ForegroundColor Gray
    
    if ($_.ScriptStackTrace) {
        Write-Host ""
        Write-Host "Stack Trace:" -ForegroundColor Gray
        Write-Host $_.ScriptStackTrace -ForegroundColor Gray
    }
    
    exit 1
} finally {
    # Cleanup
    Write-Verbose "Cleaning up test environment..."
}

# Instructions for next steps
Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Cyan
if (-not $TestOnly) {
    Write-Host "1. Restart the system to activate all hardware spoofing changes" -ForegroundColor White
    Write-Host "2. After restart, test with VM detection tools to verify effectiveness" -ForegroundColor White
    Write-Host "3. Deploy your malware samples for analysis in this stealth environment" -ForegroundColor White
} else {
    Write-Host "1. Run this script without -TestOnly to execute hardware spoofing" -ForegroundColor White
    Write-Host "2. Use -Verbose for detailed execution information" -ForegroundColor White
}
Write-Host ""

# Performance metrics
$endTime = Get-Date
Write-Host "Test completed at: $endTime" -ForegroundColor Gray
