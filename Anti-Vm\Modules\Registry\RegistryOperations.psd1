@{
    RootModule = 'RegistryOperations.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'c4d8e3f1-2b7a-4e9f-8c6d-5a3b9f2e7c1d'
    Author = 'Anti-VM Detection System'
    Description = 'Registry modification and cleanup module for VM detection bypass'
    
    FunctionsToExport = @(
        'Invoke-RegistryOperations',
        'Invoke-RegistryModification',
        'Invoke-RegistryCleanup'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}
