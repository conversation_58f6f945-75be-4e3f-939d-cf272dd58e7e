#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Quick Fix for Anti-VMDetection.ps1 Script Errors
    
.DESCRIPTION
    Fixes two main issues in the main script:
    1. Registry access permission errors
    2. C# compilation error with IntPtr const declaration
    
.NOTES
    Run this before running the main Anti-VMDetection script
#>

Write-Host "=== Fixing Anti-VMDetection Script Errors ===" -ForegroundColor Yellow

# Fix 1: Enhanced Registry Privileges
Write-Host "1. Applying enhanced registry privilege fixes..." -ForegroundColor Green

try {
    # Enable additional privileges that might be needed
    $additionalPrivileges = @(
        "SeSecurityPrivilege",
        "SeSystemEnvironmentPrivilege", 
        "SeLoadDriverPrivilege",
        "SeDebugPrivilege"
    )
    
    foreach ($privilege in $additionalPrivileges) {
        try {
            $result = [RegistryPrivileges]::EnablePrivilege($privilege)
            if ($result) {
                Write-Host "  ✓ Enabled: $privilege" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  ⚠ Could not enable: $privilege" -ForegroundColor Yellow
        }
    }
    
    # Set additional registry permissions
    $criticalRegPaths = @(
        "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}",
        "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"
    )
    
    foreach ($regPath in $criticalRegPaths) {
        if (Test-Path $regPath) {
            try {
                # Take ownership of the entire key tree
                $result = & icacls $regPath /setowner "$env:USERNAME" /t /c /q 2>&1
                
                # Grant full control
                & icacls $regPath /grant "$env:USERNAME:(F)" /t /c /q 2>&1 | Out-Null
                
                Write-Host "  ✓ Fixed permissions for: $regPath" -ForegroundColor Gray
            }
            catch {
                Write-Host "  ⚠ Permission fix failed for: $regPath" -ForegroundColor Yellow
            }
        }
    }
    
    Write-Host "Registry privilege fixes applied!" -ForegroundColor Green
}
catch {
    Write-Host "Registry privilege fix failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Fix 2: Create corrected Add-Type blocks
Write-Host "2. Creating fixed C# code blocks..." -ForegroundColor Green

# Fixed DeviceNotification class
$fixedDeviceNotificationClass = @"
using System;
using System.Runtime.InteropServices;

public class DeviceNotification {
    [DllImport("user32.dll", SetLastError = true)]
    public static extern int SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
    
    [DllImport("cfgmgr32.dll", CharSet = CharSet.Auto)]
    public static extern int CM_Locate_DevNode(out IntPtr pdnDevInst, string pDeviceID, int ulFlags);
    
    [DllImport("cfgmgr32.dll")]
    public static extern int CM_Reenumerate_DevNode(IntPtr dnDevInst, int ulFlags);
    
    public const uint WM_DEVICECHANGE = 0x0219;
    public static readonly IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;  // FIXED: Use static readonly instead of const
    public const int CM_LOCATE_DEVNODE_NORMAL = 0x00000000;
    public const int CM_REENUMERATE_NORMAL = 0x00000000;
}
"@

# Save the fixed class to a file for reference
$fixedClassPath = "C:\Users\<USER>\Desktop\antivm\FixedDeviceNotification.cs"
$fixedDeviceNotificationClass | Set-Content $fixedClassPath -Force

Write-Host "  ✓ Created fixed DeviceNotification class: $fixedClassPath" -ForegroundColor Gray

# Fix 3: Create registry access wrapper functions
$registryWrapperScript = @"
# Registry Access Wrapper Functions
# Use these functions in the main script to avoid permission errors

function Safe-GetChildItem {
    param([string]`$Path, [switch]`$Recurse)
    
    try {
        # Take ownership first
        `$regPath = `$Path -replace '^HKLM:\\', 'HKLM\' -replace '^HKCU:\\', 'HKCU\'
        & icacls "`$regPath" /setowner "`$env:USERNAME" /t /c /q 2>`$null | Out-Null
        & icacls "`$regPath" /grant "`$env:USERNAME:(F)" /t /c /q 2>`$null | Out-Null
        
        if (`$Recurse) {
            return Get-ChildItem `$Path -Recurse -ErrorAction SilentlyContinue
        } else {
            return Get-ChildItem `$Path -ErrorAction SilentlyContinue
        }
    }
    catch {
        Write-Host "Safe-GetChildItem failed for: `$Path - `$(`$_.Exception.Message)" -ForegroundColor Yellow
        return @()
    }
}

function Safe-SetRegistryProperty {
    param([string]`$Path, [string]`$Name, `$Value)
    
    try {
        # Ensure ownership
        `$regPath = `$Path -replace '^HKLM:\\', 'HKLM\' -replace '^HKCU:\\', 'HKCU\'
        & icacls "`$regPath" /setowner "`$env:USERNAME" /c /q 2>`$null | Out-Null
        & icacls "`$regPath" /grant "`$env:USERNAME:(F)" /c /q 2>`$null | Out-Null
        
        Set-ItemProperty -Path `$Path -Name `$Name -Value `$Value -Force -ErrorAction SilentlyContinue
        return `$true
    }
    catch {
        Write-Host "Safe-SetRegistryProperty failed: `$Path\`$Name - `$(`$_.Exception.Message)" -ForegroundColor Yellow
        return `$false
    }
}

Write-Host "Registry wrapper functions loaded successfully!" -ForegroundColor Green
"@

$wrapperPath = "C:\Users\<USER>\Desktop\antivm\RegistryWrappers.ps1"
$registryWrapperScript | Set-Content $wrapperPath -Force

Write-Host "  ✓ Created registry wrapper functions: $wrapperPath" -ForegroundColor Gray

Write-Host ""

# Fix 4: Create a pre-execution setup script
Write-Host "3. Creating pre-execution setup script..." -ForegroundColor Green

$preSetupScript = @"
#Requires -RunAsAdministrator

# Pre-Execution Setup for Anti-VMDetection.ps1
# Run this BEFORE running the main script to fix permission issues

Write-Host "=== Pre-Execution Setup for Anti-VMDetection ===" -ForegroundColor Cyan

# 1. Enable ALL required privileges
try {
    Add-Type @"
using System;
using System.Runtime.InteropServices;
using System.Security.Principal;
using Microsoft.Win32;

public class RegistryPrivileges
{
    [DllImport("advapi32.dll", SetLastError = true)]
    public static extern bool OpenProcessToken(IntPtr ProcessHandle, uint DesiredAccess, out IntPtr TokenHandle);
    
    [DllImport("advapi32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
    public static extern bool LookupPrivilegeValue(string lpSystemName, string lpName, out long lpLuid);
    
    [DllImport("advapi32.dll", SetLastError = true)]
    public static extern bool AdjustTokenPrivileges(IntPtr TokenHandle, bool DisableAllPrivileges, ref TOKEN_PRIVILEGES NewState, uint BufferLength, IntPtr PreviousState, IntPtr ReturnLength);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr GetCurrentProcess();
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct LUID
    {
        public uint LowPart;
        public int HighPart;
    }
    
    [StructLayout(LayoutKind.Sequential)]
    public struct TOKEN_PRIVILEGES
    {
        public uint PrivilegeCount;
        public LUID Luid;
        public uint Attributes;
    }
    
    public const uint TOKEN_ADJUST_PRIVILEGES = 0x0020;
    public const uint TOKEN_QUERY = 0x0008;
    public const uint SE_PRIVILEGE_ENABLED = 0x0002;
    public const string SE_TAKE_OWNERSHIP_NAME = "SeTakeOwnershipPrivilege";
    public const string SE_RESTORE_NAME = "SeRestorePrivilege";
    public const string SE_BACKUP_NAME = "SeBackupPrivilege";
    
    public static bool EnablePrivilege(string privilegeName)
    {
        IntPtr tokenHandle = IntPtr.Zero;
        try
        {
            if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, out tokenHandle))
                return false;
                
            long luid;
            if (!LookupPrivilegeValue(null, privilegeName, out luid))
                return false;
                
            TOKEN_PRIVILEGES tokenPrivileges = new TOKEN_PRIVILEGES();
            tokenPrivileges.PrivilegeCount = 1;
            tokenPrivileges.Luid.LowPart = (uint)luid;
            tokenPrivileges.Luid.HighPart = (int)(luid >> 32);
            tokenPrivileges.Attributes = SE_PRIVILEGE_ENABLED;
            
            return AdjustTokenPrivileges(tokenHandle, false, ref tokenPrivileges, 0, IntPtr.Zero, IntPtr.Zero);
        }
        finally
        {
            if (tokenHandle != IntPtr.Zero)
                CloseHandle(tokenHandle);
        }
    }
}
"@ -ErrorAction SilentlyContinue

    `$allPrivileges = @(
        "SeTakeOwnershipPrivilege",
        "SeRestorePrivilege", 
        "SeBackupPrivilege",
        "SeSecurityPrivilege",
        "SeSystemEnvironmentPrivilege",
        "SeLoadDriverPrivilege",
        "SeDebugPrivilege"
    )
    
    foreach (`$privilege in `$allPrivileges) {
        `$result = [RegistryPrivileges]::EnablePrivilege(`$privilege)
        if (`$result) {
            Write-Host "✓ Enabled: `$privilege" -ForegroundColor Green
        } else {
            Write-Host "⚠ Failed: `$privilege" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "Privilege setup failed: `$(`$_.Exception.Message)" -ForegroundColor Red
}

# 2. Pre-configure critical registry permissions
Write-Host "`nConfiguring registry permissions..." -ForegroundColor Cyan

`$criticalPaths = @(
    "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}",
    "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}",
    "HKLM\SYSTEM\CurrentControlSet\Enum\PCI",
    "HKLM\SYSTEM\CurrentControlSet\Enum\USB",
    "HKLM\SYSTEM\CurrentControlSet\Services",
    "HKLM\SOFTWARE\VMware, Inc.",
    "HKLM\HARDWARE\DESCRIPTION\System"
)

foreach (`$regPath in `$criticalPaths) {
    try {
        # Take ownership and grant permissions
        & takeown /f "`$regPath" /r /d Y /a 2>`$null | Out-Null
        & icacls "`$regPath" /setowner "Administrators" /t /c /q 2>`$null | Out-Null
        & icacls "`$regPath" /grant "Administrators:(F)" /t /c /q 2>`$null | Out-Null
        & icacls "`$regPath" /grant "`$env:USERNAME:(F)" /t /c /q 2>`$null | Out-Null
        
        Write-Host "✓ Fixed: `$regPath" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ Failed: `$regPath" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Setup Complete! ===" -ForegroundColor Cyan
Write-Host "You can now run the main Anti-VMDetection.ps1 script" -ForegroundColor Green
Write-Host "The registry permission errors should be resolved." -ForegroundColor Green
"@

$preSetupPath = "C:\Users\<USER>\Desktop\antivm\Pre-Setup-Fix.ps1"
$preSetupScript | Set-Content $preSetupPath -Force

Write-Host "  ✓ Created pre-setup script: $preSetupPath" -ForegroundColor Gray

Write-Host ""

# Fix 5: Create the actual fixes to apply to main script
Write-Host "4. Creating direct script fixes..." -ForegroundColor Green

$scriptFixes = @"
# FIXES TO APPLY TO MAIN ANTI-VMDETECTION SCRIPT
# These are the exact changes needed to fix the errors

# FIX 1: Replace the Add-Type block around line 636 with this corrected version:
#        Change: public const IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;
#        To:     public static readonly IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;

# FIX 2: Add this error handling wrapper for Get-ChildItem operations:

function Safe-Get-ChildItem {
    param([string]`$Path, [switch]`$Recurse, [int]`$MaxAttempts = 3)
    
    for (`$attempt = 1; `$attempt -le `$MaxAttempts; `$attempt++) {
        try {
            # Take ownership first
            `$regPath = `$Path -replace '^HKLM:\\', 'HKLM\' -replace '^HKCU:\\', 'HKCU\'
            & takeown /f "`$regPath" /r /d Y /a 2>`$null | Out-Null
            & icacls "`$regPath" /grant "`$env:USERNAME:(F)" /t /c /q 2>`$null | Out-Null
            
            if (`$Recurse) {
                return Get-ChildItem `$Path -Recurse -ErrorAction Stop
            } else {
                return Get-ChildItem `$Path -ErrorAction Stop
            }
        }
        catch {
            if (`$attempt -eq `$MaxAttempts) {
                Write-Host "PERMISSION ERROR: Could not access `$Path after `$MaxAttempts attempts" -ForegroundColor Red
                return @()
            }
            Start-Sleep -Seconds 1
        }
    }
}

# FIX 3: Use this pattern to replace problematic Get-ChildItem calls:
# OLD: Get-ChildItem `$displayClassPath | Where-Object { `$_.Name -match "[0-9]{4}" }
# NEW: Safe-Get-ChildItem `$displayClassPath | Where-Object { `$_.Name -match "[0-9]{4}" }

Write-Host "Script fixes documented in: script-fixes.txt"
"@

$fixesPath = "C:\Users\<USER>\Desktop\antivm\script-fixes.txt"
$scriptFixes | Set-Content $fixesPath -Force

Write-Host "  ✓ Created fix documentation: $fixesPath" -ForegroundColor Gray

Write-Host ""

# Create the corrected Add-Type block as a separate file
Write-Host "5. Creating corrected Add-Type block..." -ForegroundColor Green

$correctedAddType = @"
# CORRECTED Add-Type block to replace the one causing compilation errors
# Use this in the main script around line 636

try {
    Add-Type @"
using System;
using System.Runtime.InteropServices;

public class DeviceNotification {
    [DllImport("user32.dll", SetLastError = true)]
    public static extern int SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
    
    [DllImport("cfgmgr32.dll", CharSet = CharSet.Auto)]
    public static extern int CM_Locate_DevNode(out IntPtr pdnDevInst, string pDeviceID, int ulFlags);
    
    [DllImport("cfgmgr32.dll")]
    public static extern int CM_Reenumerate_DevNode(IntPtr dnDevInst, int ulFlags);
    
    public const uint WM_DEVICECHANGE = 0x0219;
    public static readonly IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;  // FIXED: static readonly instead of const
    public const int CM_LOCATE_DEVNODE_NORMAL = 0x00000000;
    public const int CM_REENUMERATE_NORMAL = 0x00000000;
}
"@ -ErrorAction SilentlyContinue

    # Trigger device re-enumeration
    [DeviceNotification]::SendMessage([DeviceNotification]::HWND_BROADCAST, [DeviceNotification]::WM_DEVICECHANGE, [IntPtr]::Zero, [IntPtr]::Zero)
    
    Write-Host "Device notification class compiled successfully" -ForegroundColor Green
}
catch {
    Write-Host "Add-Type compilation failed: `$(`$_.Exception.Message)" -ForegroundColor Red
}
"@

$correctedAddTypePath = "C:\Users\<USER>\Desktop\antivm\CorrectedAddType.ps1"
$correctedAddType | Set-Content $correctedAddTypePath -Force

Write-Host "  ✓ Created corrected Add-Type block: $correctedAddTypePath" -ForegroundColor Gray

Write-Host ""

# Summary and instructions
Write-Host "=== FIXES SUMMARY ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "IMMEDIATE SOLUTION (Quick Fix):" -ForegroundColor Cyan
Write-Host "1. Run this first: .\Pre-Setup-Fix.ps1" -ForegroundColor White
Write-Host "2. Then run your main script: .\Anti-VMDetection.ps1" -ForegroundColor White
Write-Host ""
Write-Host "PERMANENT SOLUTION (Edit main script):" -ForegroundColor Cyan
Write-Host "1. In Anti-VMDetection.ps1 around line 636, find:" -ForegroundColor White
Write-Host "   public const IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;" -ForegroundColor Red
Write-Host "2. Replace with:" -ForegroundColor White
Write-Host "   public static readonly IntPtr HWND_BROADCAST = (IntPtr)0xFFFF;" -ForegroundColor Green
Write-Host ""
Write-Host "3. Replace all Get-ChildItem calls in problematic sections with Safe-Get-ChildItem" -ForegroundColor White
Write-Host "   (Load the wrapper functions from RegistryWrappers.ps1 first)" -ForegroundColor Gray
Write-Host ""
Write-Host "FILES CREATED:" -ForegroundColor Yellow
Write-Host "• Pre-Setup-Fix.ps1        - Run this before main script" -ForegroundColor Gray
Write-Host "• RegistryWrappers.ps1     - Safe registry access functions" -ForegroundColor Gray  
Write-Host "• CorrectedAddType.ps1     - Fixed C# compilation code" -ForegroundColor Gray
Write-Host "• script-fixes.txt         - Detailed fix instructions" -ForegroundColor Gray
Write-Host ""
Write-Host "RECOMMENDED WORKFLOW:" -ForegroundColor Cyan
Write-Host "1. .\Pre-Setup-Fix.ps1" -ForegroundColor White
Write-Host "2. .\Anti-VMDetection.ps1" -ForegroundColor White  
Write-Host "3. .\VMware-Display-Fix.ps1 -Method SelectiveRestore" -ForegroundColor White
