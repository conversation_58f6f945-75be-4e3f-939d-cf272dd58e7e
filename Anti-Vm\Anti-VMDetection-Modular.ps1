#Requires -RunAsAdministrator
#Requires -Version 5.1

<#
.SYNOPSIS
    Anti-VM Detection Toolkit - Modular Architecture Version

.DESCRIPTION
    Advanced modular anti-VM detection bypass system for cybersecurity research.
    This is the main orchestration script that dynamically loads and executes
    individual modules based on configuration settings.

    Target Malware Families (2024-2025):
    - LockBit, BlackCat/ALPHV, Royal Ransomware (VM-aware variants)
    - Emote<PERSON>, Qakbot, IcedID (banking trojans with VM detection)
    - Cobalt Strike beacons, Metasploit payloads
    - APT groups using VM-aware implants (APT29, APT40, Lazarus)

.PARAMETER ConfigFile
    Path to configuration file (default: config.psd1)

.PARAMETER LogLevel
    Logging verbosity: Debug, Info, Warning, Error (default: from config)

.PARAMETER BackupPath
    Directory for storing system backups (default: .\Backups)

.PARAMETER RollbackMode
    Restore system from previous backup

.PARAMETER ModulesPath
    Path to modules directory (default: .\Modules)

.PARAMETER DryRun
    Simulate execution without making actual changes

.EXAMPLE
    .\Anti-VMDetection-Modular.ps1 -LogLevel Debug
    .\Anti-VMDetection-Modular.ps1 -RollbackMode -BackupPath "D:\Backups\********"
    .\Anti-VMDetection-Modular.ps1 -DryRun -ConfigFile "custom-config.psd1"

.NOTES
    Author: Cybersecurity Research Team
    Version: 2.0-Modular
    Requires: Windows 10/11, PowerShell 5.1+, Administrative privileges
    Tested: VMware Workstation 16+/17+

    WARNING: This tool modifies critical system components. Use only in isolated
    research environments. Create full system backups before deployment.
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigFile = "config.psd1",
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("Debug", "Info", "Warning", "Error")]
    [string]$LogLevel,
    
    [Parameter(Mandatory = $false)]
    [string]$BackupPath = ".\Backups",
    
    [Parameter(Mandatory = $false)]
    [switch]$RollbackMode,
    
    [Parameter(Mandatory = $false)]
    [string]$ModulesPath = ".\Modules",
    
    [Parameter(Mandatory = $false)]
    [switch]$DryRun
)

# Global script variables
$script:LoadedModules = @{}
$script:ModuleExecutionResults = @{}
$script:GlobalConfig = $null
$script:BackupLocation = $null
$script:StartTime = Get-Date
$script:ModifiedComponents = @()

#region Module Loading and Management

function Import-AntiVMModule {
    <#
    .SYNOPSIS
        Dynamically imports a module from the modules directory
    
    .PARAMETER ModuleName
        Module category name (e.g., "Hardware", "Registry")
        
    .PARAMETER ModulesBasePath
        Base path for modules directory
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ModuleName,
        
        [Parameter(Mandatory = $true)]
        [string]$ModulesBasePath
    )
    
    try {
        # Map module names to actual module files
        $moduleMap = @{
            'Core' = @{
                'Logging' = 'Core\Logging\Logging.psm1'
                'Configuration' = 'Core\Configuration\Configuration.psm1'
                'Validation' = 'Core\Validation\Validation.psm1'
                'Utilities' = 'Core\Utilities\Utilities.psm1'
            }
            'Registry' = 'Registry\RegistryOperations.psm1'
            'Hardware' = 'Hardware\HardwareSpoofing.psm1'
            'System' = 'System\SystemSpoofing.psm1'
            'FileSystem' = 'FileSystem\FileSystemOperations.psm1'
            'Behavioral' = 'Behavioral\BehavioralEvasion.psm1'
            'Advanced' = 'Advanced\AdvancedBypass.psm1'
            'Device' = 'Device\DeviceIdentification\DeviceIdentification.psm1'
            'Recovery' = 'Recovery\RecoveryOperations.psm1'
        }
        
        if ($ModuleName -eq 'Core') {
            # Load all core modules
            foreach ($coreModule in $moduleMap['Core'].GetEnumerator()) {
                $moduleFile = Join-Path $ModulesBasePath $coreModule.Value
                if (Test-Path $moduleFile) {
                    $importedModule = Import-Module $moduleFile -Force -PassThru -ErrorAction Stop
                    $script:LoadedModules["Core.$($coreModule.Key)"] = $importedModule
                    Write-Host "[INFO] Loaded core module: $($coreModule.Key)" -ForegroundColor Green
                }
            }
        }
        else {
            # Load specific module
            $moduleFile = Join-Path $ModulesBasePath $moduleMap[$ModuleName]
            
            if (-not (Test-Path $moduleFile)) {
                throw "Module file not found: $moduleFile"
            }
            
            # Import the module
            $importedModule = Import-Module $moduleFile -Force -PassThru -ErrorAction Stop
            $script:LoadedModules[$ModuleName] = $importedModule
            
            Write-Host "[INFO] Loaded module: $ModuleName" -ForegroundColor Green
            return $importedModule
        }
    }
    catch {
        Write-Host "[ERROR] Failed to load module $ModuleName : $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

function Initialize-CoreModules {
    <#
    .SYNOPSIS
        Initializes all core infrastructure modules
    #>
    [CmdletBinding()]
    param()
    
    try {
        Write-Host "[INFO] Initializing core infrastructure modules..." -ForegroundColor Cyan
        
        # Load core modules
        Import-AntiVMModule -ModuleName "Core" -ModulesBasePath $ModulesPath
        
        # Initialize logging first
        if ($script:LoadedModules.ContainsKey("Core.Logging")) {
            $tempConfig = @{ Modules = @{ Core = @{ Logging = @{ 
                Enabled = $true
                LogLevel = if ($LogLevel) { $LogLevel } else { "Info" }
                LogToFile = $true
                LogToConsole = $true
                ShowProgress = $true
            }}}}
            Initialize-Logging -Config $tempConfig
        }
        
        Write-ModuleLog "Core modules initialization completed" "Info"
        return $true
    }
    catch {
        Write-Host "[ERROR] Core modules initialization failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Import-ConfiguredModules {
    <#
    .SYNOPSIS
        Imports modules based on configuration settings
    #>
    [CmdletBinding()]
    param()
    
    try {
        Write-ModuleLog "Loading modules based on configuration..." "Info"
        
        # Get load order from configuration
        $loadOrder = $script:GlobalConfig.LoadOrder
        $loadedCount = 0
        
        foreach ($moduleName in $loadOrder) {
            try {
                # Skip core modules (already loaded)
                if ($moduleName -eq "Core") {
                    continue
                }
                
                # Check if module category is enabled
                $moduleConfig = $script:GlobalConfig.Modules[$moduleName]
                if (-not $moduleConfig) {
                    Write-ModuleLog "Module configuration not found: $moduleName" "Warning"
                    continue
                }
                
                # Check if any sub-modules in category are enabled
                $hasEnabledSubmodules = $false
                foreach ($subModule in $moduleConfig.GetEnumerator()) {
                    if ($subModule.Value.Enabled) {
                        $hasEnabledSubmodules = $true
                        break
                    }
                }
                
                if ($hasEnabledSubmodules) {
                    # Load the module if not already loaded
                    if (-not $script:LoadedModules.ContainsKey($moduleName)) {
                        Import-AntiVMModule -ModuleName $moduleName -ModulesBasePath $ModulesPath
                        $loadedCount++
                    }
                } else {
                    Write-ModuleLog "Module category disabled: $moduleName" "Debug"
                }
            }
            catch {
                Write-ModuleLog "Failed to load/initialize module $moduleName : $($_.Exception.Message)" "Warning"
                # Continue with other modules
            }
        }
        
        Write-ModuleLog "Module loading completed. Loaded $loadedCount modules." "Info"
        return $true
    }
    catch {
        Write-ModuleLog "Module loading failed: $($_.Exception.Message)" "Error"
        return $false
    }
}

#endregion

#region Execution Engine

function Start-ModularAntiVMDetection {
    <#
    .SYNOPSIS
        Main execution function for the modular anti-VM detection system
    #>
    [CmdletBinding()]
    param()
    
    Write-ModuleLog "=== Anti-VM Detection Toolkit (Modular) Started ===" "Info"
    Write-ModuleLog "Target: Modern malware VM detection bypass for cybersecurity research" "Info"
    Write-ModuleLog "Environment: VMware Workstation 16+/17+ on Windows 10/11" "Info"
    
    if ($DryRun) {
        Write-ModuleLog "DRY RUN MODE: No actual changes will be made" "Warning"
    }
    
    try {
        # Validate prerequisites using core validation module
        if ($script:LoadedModules.ContainsKey("Core.Validation")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Validating Prerequisites" -PercentComplete 5
            Invoke-PrerequisiteValidation
        }
        
        # Enable registry privileges
        if ($script:LoadedModules.ContainsKey("Registry.RegistryPrivileges")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Enabling Registry Privileges" -PercentComplete 10
            Enable-RegistryPrivileges
        }
        
        # Create system backup
        if ($script:GlobalConfig.safety.createBackups -and $script:LoadedModules.ContainsKey("Recovery.Backup")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Creating System Backup" -PercentComplete 15
            $script:BackupLocation = New-SystemBackup -BackupDir $BackupPath
            Write-Log "System backup created at: $script:BackupLocation" -Level Info
        }
        
        # Confirm execution if required
        if ($script:GlobalConfig.safety.requireConfirmation -and -not $DryRun) {
            $confirmation = Read-Host "This will modify critical system components. Continue? (y/N)"
            if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
                Write-Log "Operation cancelled by user" -Level Info
                return
            }
        }
        
        # Execute modules in configured order
        $progressStep = 70 / $script:GlobalConfig.LoadOrder.Count
        $currentProgress = 20
        
        foreach ($moduleName in $script:GlobalConfig.LoadOrder) {
            try {
                # Skip core modules (already executed)
                if ($moduleName -eq "Core") {
                    continue
                }
                
                # Check if module category is enabled
                $moduleConfig = $script:GlobalConfig.Modules[$moduleName]
                if (-not $moduleConfig) {
                    Write-ModuleLog "Module configuration not found: $moduleName" "Warning"
                    continue
                }
                
                # Check if any sub-modules in category are enabled
                $hasEnabledSubmodules = $false
                foreach ($subModule in $moduleConfig.GetEnumerator()) {
                    if ($subModule.Value.Enabled) {
                        $hasEnabledSubmodules = $true
                        break
                    }
                }
                
                if (-not $hasEnabledSubmodules) {
                    Write-ModuleLog "Skipping disabled module category: $moduleName" "Debug"
                    continue
                }
                
                Write-Progress-Log -Activity "Anti-VM Detection" -Status "Executing $moduleName" -PercentComplete $currentProgress
                
                # Execute the main function for this module
                $mainFunctionName = "Invoke-$moduleName"
                $functionExists = Get-Command $mainFunctionName -ErrorAction SilentlyContinue
                
                if ($functionExists) {
                    if ($DryRun) {
                        Write-ModuleLog "DRY RUN: Would execute $mainFunctionName" "Info"
                        $result = @{ Success = $true; DryRun = $true }
                    } else {
                        Write-ModuleLog "Executing module: $moduleName" "Info"
                        $result = & $mainFunctionName -Config $script:GlobalConfig
                        Write-ModuleLog "Module $moduleName completed successfully" "Info"
                    }
                    
                    $script:ModuleExecutionResults[$moduleName] = $result
                } else {
                    Write-ModuleLog "Main function not found for module: $moduleName ($mainFunctionName)" "Warning"
                }
                
                $currentProgress += $progressStep
            }
            catch {
                Write-ModuleLog "Module execution failed: $moduleName - $($_.Exception.Message)" "Error"
                $script:ModuleExecutionResults[$moduleName] = @{ Success = $false; Error = $_.Exception.Message }
                
                if ($script:GlobalConfig.execution.stopOnCriticalError) {
                    throw "Critical module failure: $moduleName"
                }
            }
        }
        
        Write-Progress-Log -Activity "Anti-VM Detection" -Status "Finalizing" -PercentComplete 95
        
        # Generate execution summary
        Write-ExecutionSummary
        
        Write-Progress-Log -Activity "Anti-VM Detection" -Status "Complete" -PercentComplete 100
        Write-Log "=== Anti-VM Detection Toolkit (Modular) Completed Successfully ===" -Level Info
        
        if (-not $DryRun) {
            Write-Log "IMPORTANT: System restart recommended for full effect" -Level Warning
            Write-Log "REMINDER: Use rollback function to restore original state when analysis is complete" -Level Warning
        }
    }
    catch {
        Write-Log "Critical error during execution: $($_.Exception.Message)" -Level Error
        if ($script:BackupLocation) {
            Write-Log "Check backup at $script:BackupLocation for recovery options" -Level Error
        }
        throw
    }
}

function Write-ExecutionSummary {
    <#
    .SYNOPSIS
        Generates and displays execution summary
    #>
    [CmdletBinding()]
    param()
    
    Write-Log "=== EXECUTION SUMMARY ===" -Level Info
    
    # Module execution statistics
    $totalModules = $script:ModuleExecutionResults.Count
    $successfulModules = ($script:ModuleExecutionResults.Values | Where-Object { $_.Success -eq $true }).Count
    $failedModules = $totalModules - $successfulModules
    
    Write-Log "Modules executed: $totalModules" -Level Info
    Write-Log "Successful: $successfulModules" -Level Info
    Write-Log "Failed: $failedModules" -Level Info
    
    if ($script:BackupLocation) {
        Write-Log "Backup location: $script:BackupLocation" -Level Info
    }
    Write-Log "Log file: $(Get-LogFile)" -Level Info
    Write-Log "Execution time: $((Get-Date) - $script:StartTime)" -Level Info
    
    # Show failed modules
    if ($failedModules -gt 0) {
        Write-Log "Failed modules:" -Level Warning
        $script:ModuleExecutionResults.GetEnumerator() | Where-Object { $_.Value.Success -eq $false } | ForEach-Object {
            Write-Log "  - $($_.Key): $($_.Value.Error)" -Level Warning
        }
    }
    
    # Aggregate modified components
    $allComponents = @()
    $script:ModuleExecutionResults.Values | ForEach-Object {
        if ($_.ModifiedComponents) {
            $allComponents += $_.ModifiedComponents
        }
    }
    
    Write-Log "Total components modified: $($allComponents.Count)" -Level Info
    if ($script:GlobalConfig.output.showModifiedComponents -and $allComponents.Count -gt 0) {
        Write-Log "Modified components:" -Level Info
        $allComponents | ForEach-Object { Write-Log "  - $_" -Level Info }
    }
}

#endregion

#region Rollback Operations

function Start-SystemRollback {
    <#
    .SYNOPSIS
        Performs system rollback using the Recovery modules
    
    .PARAMETER BackupDirectory
        Directory containing backup files
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$BackupDirectory
    )
    
    try {
        Write-Log "Starting system rollback from backup: $BackupDirectory" -Level Info
        
        # Load core modules for rollback
        if (-not (Initialize-CoreModules)) {
            throw "Failed to initialize core modules for rollback"
        }
        
        # Load configuration for rollback settings
        $script:GlobalConfig = Load-AntiVMConfiguration -ConfigPath $ConfigFile
        
        # Load rollback module
        Import-AntiVMModule -ModuleName "Recovery.Rollback" -ModulesBasePath $ModulesPath
        Initialize-RollbackOperations -Config $script:GlobalConfig
        
        # Perform rollback
        Invoke-SystemRollback -BackupDir $BackupDirectory
        
        Write-Log "System rollback completed successfully" -Level Info
        Write-Log "IMPORTANT: System restart recommended to complete restoration" -Level Warning
    }
    catch {
        Write-Log "System rollback failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

#endregion

#region Main Execution

try {
    # Initialize script
    Write-Host "Anti-VM Detection Toolkit - Modular Architecture v2.0" -ForegroundColor Cyan
    Write-Host "Cybersecurity Research Team" -ForegroundColor Gray
    Write-Host ""
    
    # Handle rollback mode
    if ($RollbackMode) {
        if (-not $BackupPath -or -not (Test-Path $BackupPath)) {
            throw "Valid backup path required for rollback mode"
        }
        Start-SystemRollback -BackupDirectory $BackupPath
        exit 0
    }
    
    # Validate modules directory
    if (-not (Test-Path $ModulesPath)) {
        throw "Modules directory not found: $ModulesPath"
    }
    
    # Initialize core infrastructure
    Write-Host "[INFO] Initializing core infrastructure..." -ForegroundColor Cyan
    if (-not (Initialize-CoreModules)) {
        throw "Failed to initialize core modules"
    }
    
    # Load configuration
    Write-Log "Loading configuration from: $ConfigFile" -Level Info
    $script:GlobalConfig = Load-AntiVMConfiguration -ConfigPath $ConfigFile
    
    # Override log level if specified via parameter
    if ($LogLevel) {
        $script:GlobalConfig.Modules.Core.Logging.LogLevel = $LogLevel
        Write-ModuleLog "Log level overridden to: $LogLevel" "Info"
    }
    
    # Re-initialize logging with full configuration
    Initialize-Logging -Config $script:GlobalConfig
    
    # Load and initialize all configured modules
    Write-Log "Loading configured modules..." -Level Info
    if (-not (Import-ConfiguredModules)) {
        throw "Failed to load configured modules"
    }
    
    # Start main execution
    Start-ModularAntiVMDetection
    
    # Performance and stability monitoring
    if ($script:GlobalConfig.safety.performStabilityChecks -and -not $DryRun) {
        Write-Log "Performing post-execution stability check..." -Level Info
        
        # Check critical services
        $criticalServices = @("Winmgmt", "EventLog", "RpcSs", "Dhcp")
        foreach ($service in $criticalServices) {
            $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
            if ($svc -and $svc.Status -ne "Running") {
                Write-Log "WARNING: Critical service not running: $service" -Level Warning
            }
        }
        
        # Check system responsiveness
        $responseTest = Measure-Command { Get-Process | Out-Null }
        if ($responseTest.TotalSeconds -gt 5) {
            Write-Log "WARNING: System response time degraded ($($responseTest.TotalSeconds)s)" -Level Warning
        }
        
        Write-Log "Stability check completed" -Level Info
    }
    
    Write-Log "Script execution completed successfully. Log saved to: $(Get-LogFile)" -Level Info
}
catch {
    $errorMessage = "Script execution failed: $($_.Exception.Message)"
    
    if (Get-Command Write-Log -ErrorAction SilentlyContinue) {
        Write-Log $errorMessage -Level Error
    } else {
        Write-Host "[ERROR] $errorMessage" -ForegroundColor Red
    }
    
    exit 1
}
finally {
    # Cleanup: Remove loaded modules if needed
    if ($script:LoadedModules.Count -gt 0) {
        Write-Log "Cleaning up loaded modules..." -Level Debug
        $script:LoadedModules.Values | ForEach-Object {
            try {
                Remove-Module $_.Name -Force -ErrorAction SilentlyContinue
            }
            catch {
                # Ignore cleanup errors
            }
        }
    }
}

#endregion
