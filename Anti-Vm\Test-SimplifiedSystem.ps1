# Test Script for Simplified Modular Anti-VM Detection System

Write-Host "Testing Simplified Modular Anti-VM Detection System..." -ForegroundColor Cyan
Write-Host ""

# Test 1: Check directory structure
Write-Host "[TEST 1] Checking directory structure..." -ForegroundColor Yellow
$requiredDirs = @(
    "Modules\Advanced",
    "Modules\Behavioral", 
    "Modules\Core\Logging",
    "Modules\Core\Configuration",
    "Modules\Core\Utilities",
    "Modules\Core\Validation",
    "Modules\FileSystem",
    "Modules\Hardware",
    "Modules\Recovery",
    "Modules\Registry",
    "Modules\System"
)

$missingDirs = @()
foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        $missingDirs += $dir
    }
}

if ($missingDirs.Count -eq 0) {
    Write-Host "[OK] All required directories present" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Missing directories: $($missingDirs -join ', ')" -ForegroundColor Red
}

# Test 2: Check essential module files
Write-Host "`n[TEST 2] Checking essential module files..." -ForegroundColor Yellow
$requiredModules = @(
    "Modules\Advanced\AdvancedBypass.psm1",
    "Modules\Behavioral\BehavioralEvasion.psm1",
    "Modules\Core\Logging\Logging.psm1",
    "Modules\FileSystem\FileSystemOperations.psm1",
    "Modules\Hardware\HardwareSpoofing.psm1",
    "Modules\Recovery\RecoveryOperations.psm1",
    "Modules\Registry\RegistryOperations.psm1",
    "Modules\System\SystemSpoofing.psm1"
)

$missingModules = @()
foreach ($module in $requiredModules) {
    if (-not (Test-Path $module)) {
        $missingModules += $module
    }
}

if ($missingModules.Count -eq 0) {
    Write-Host "[OK] All essential module files present" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Missing modules: $($missingModules -join ', ')" -ForegroundColor Red
}

# Test 3: Check configuration file
Write-Host "`n[TEST 3] Checking configuration file..." -ForegroundColor Yellow
if (Test-Path "config.psd1") {
    try {
        $config = Import-PowerShellDataFile -Path "config.psd1"
        if ($config.Modules -and $config.HardwareSpecs -and $config.LoadOrder) {
            Write-Host "[OK] Configuration file valid and properly structured" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] Configuration file missing required sections" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "[ERROR] Configuration file syntax error: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[ERROR] Configuration file not found" -ForegroundColor Red
}

# Test 4: Check main orchestration script
Write-Host "`n[TEST 4] Checking main orchestration script..." -ForegroundColor Yellow
if (Test-Path "Anti-VMDetection-Modular.ps1") {
    try {
        $scriptContent = Get-Content "Anti-VMDetection-Modular.ps1" -Raw
        if ($scriptContent.Length -gt 1000) {
            Write-Host "[OK] Main orchestration script accessible" -ForegroundColor Green
        } else {
            Write-Host "[ERROR] Main orchestration script too small" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "[ERROR] Main orchestration script has issues: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[ERROR] Main orchestration script not found" -ForegroundColor Red
}

# Test 5: Test module syntax
Write-Host "`n[TEST 5] Testing module syntax..." -ForegroundColor Yellow
$moduleFiles = Get-ChildItem "Modules" -Filter "*.psm1" -Recurse
$syntaxErrors = @()

foreach ($moduleFile in $moduleFiles) {
    try {
        $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $moduleFile.FullName -Raw), [ref]$null)
    }
    catch {
        $syntaxErrors += "$($moduleFile.FullName): $($_.Exception.Message)"
    }
}

if ($syntaxErrors.Count -eq 0) {
    Write-Host "[OK] All modules have valid syntax ($($moduleFiles.Count) modules checked)" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Syntax errors found:" -ForegroundColor Red
    $syntaxErrors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
}

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "SIMPLIFIED MODULAR SYSTEM TEST SUMMARY" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan

$totalTests = 5
$passedTests = 0

if ($missingDirs.Count -eq 0) { $passedTests++ }
if ($missingModules.Count -eq 0) { $passedTests++ }
if (Test-Path "config.psd1") { 
    try { 
        $config = Import-PowerShellDataFile "config.psd1"
        if ($config.Modules) { $passedTests++ }
    } catch { }
}
if (Test-Path "Anti-VMDetection-Modular.ps1") { $passedTests++ }
if ($syntaxErrors.Count -eq 0) { $passedTests++ }

Write-Host "Tests Passed: $passedTests/$totalTests" -ForegroundColor $(if($passedTests -eq $totalTests){"Green"}else{"Yellow"})

if ($passedTests -eq $totalTests) {
    Write-Host "`nSYSTEM READY FOR USE!" -ForegroundColor Green
    Write-Host "You can now run: .\Anti-VMDetection-Modular.ps1 -DryRun" -ForegroundColor Cyan
} else {
    Write-Host "`nSystem requires fixes before use." -ForegroundColor Red
}

Write-Host ""
