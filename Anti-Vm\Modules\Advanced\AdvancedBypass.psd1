@{
    RootModule = 'AdvancedBypass.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'a9c3f7e2-6d8b-4f1e-9c7a-5b3f8d2e6a1c'
    Author = 'Anti-VM Detection System'
    Description = 'Advanced detection bypass module for Hypervisor, Memory, Environment, and Timing attacks'
    
    FunctionsToExport = @(
        'Invoke-AdvancedBypass',
        'Invoke-HypervisorBypass',
        'Invoke-MemoryCleanup',
        'Invoke-EnvironmentManipulation',
        'Invoke-TimingAttackPrevention'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}
