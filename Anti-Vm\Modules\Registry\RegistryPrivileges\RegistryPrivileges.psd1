@{
    RootModule = 'RegistryPrivileges.psm1'
    ModuleVersion = '2.0.0'
    CompatiblePSEditions = @('Desktop', 'Core')
    GUID = 'e1f2a3b4-c5d6-7890-1234-ef0123456789'
    Author = 'Cybersecurity Research Team'
    CompanyName = 'Anti-VM Detection Toolkit'
    Copyright = '(c) 2025 Cybersecurity Research Team. All rights reserved.'
    Description = 'Registry Privileges Module - Provides advanced registry access through C# interop.'
    PowerShellVersion = '5.1'
    ClrVersion = '4.0'
    
    FunctionsToExport = @(
        'Initialize-RegistryPrivileges',
        'Enable-RegistryPrivileges',
        'Set-RegistryKeyOwnership',
        'Test-RegistryPrivilegesEnabled',
        'Get-RegistryPrivilegesStatus'
    )
    
    CmdletsToExport = @()
    VariablesToExport = @()
    AliasesToExport = @()
    
    PrivateData = @{
        PSData = @{
            Tags = @('AntiVM', 'RegistryPrivileges', 'Cybersecurity', 'Research')
            ReleaseNotes = 'RegistryPrivileges Module v2.0 - Modular architecture implementation'
            ExternalModuleDependencies = @()
        }
    }
}
