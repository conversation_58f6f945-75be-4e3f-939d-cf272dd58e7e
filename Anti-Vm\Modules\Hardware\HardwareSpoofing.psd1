@{
    RootModule = 'HardwareSpoofing.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'b8f7e2d4-9a6c-4e3f-8b2d-1c5a7f9e3b4d'
    Author = 'Anti-VM Detection System'
    Description = 'Comprehensive hardware spoofing module for GPU, Storage, Memory, Motherboard, and Network components'
    
    FunctionsToExport = @(
        'Invoke-HardwareSpoofing',
        'Invoke-GPUSpoofing', 
        'Invoke-StorageSpoofing',
        'Invoke-MemorySpoofing',
        'Invoke-MotherboardSpoofing',
        'Invoke-NetworkSpoofing'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' },
        @{ ModuleName = 'Microsoft.PowerShell.Utility'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}
