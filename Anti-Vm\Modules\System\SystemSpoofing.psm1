# System Spoofing Module
# BIOS, Drivers, and Services spoofing for VM detection bypass

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\Core\Utilities\Utilities.psm1" -Force

# BIOS Spoofing Functions
function Invoke-BIOSSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting BIOS spoofing..." "Info"
    
    try {
        $biosSpecs = $Config.HardwareSpecs.Motherboard
        
        # Spoof BIOS information
        $biosKeys = @(
            'HKLM:\HARDWARE\DESCRIPTION\System\BIOS',
            'HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation'
        )
        
        foreach ($keyPath in $biosKeys) {
            if (Test-Path $keyPath) {
                # Set BIOS identifiers
                Set-RegistryValue -Path $keyPath -Name "BIOSVendor" -Value $biosSpecs.BIOSVendor -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSVersion" -Value $biosSpecs.BIOSVersion -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSReleaseDate" -Value $biosSpecs.BIOSDate -Type "String"
                
                # Generate realistic BIOS serial
                $biosSerial = Get-RandomSerial -Manufacturer $biosSpecs.BIOSVendor
                Set-RegistryValue -Path $keyPath -Name "BIOSSerialNumber" -Value $biosSerial -Type "String"
                
                # Remove VM-specific BIOS signatures
                $vmBiosSignatures = @('VBOX', 'VMWARE', 'QEMU', 'BOCHS', 'SEABIOS')
                foreach ($signature in $vmBiosSignatures) {
                    try {
                        Remove-ItemProperty -Path $keyPath -Name $signature -ErrorAction SilentlyContinue
                    }
                    catch {
                        # Ignore errors for non-existent properties
                    }
                }
                
                Write-ModuleLog "Updated BIOS information in: $keyPath" "Debug"
            }
        }
        
        Write-ModuleLog "BIOS spoofing completed successfully" "Info"
        return @{ Success = $true; Message = "BIOS spoofed to $($biosSpecs.BIOSVendor) $($biosSpecs.BIOSVersion)" }
    }
    catch {
        Write-ModuleLog "BIOS spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Driver Spoofing Functions
function Invoke-DriverSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting driver spoofing..." "Info"
    
    try {
        # Spoof driver information to remove VM signatures
        $driverKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Services',
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class'
        )
        
        $vmDriverPatterns = @('vbox', 'vmware', 'qemu', 'virtual', 'hyper-v')
        $spoofedCount = 0
        
        foreach ($keyPath in $driverKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $currentPath = $subKey.PSPath
                    
                    # Check for VM driver signatures in driver descriptions
                    try {
                        $driverDesc = Get-ItemProperty -Path $currentPath -Name "DriverDesc" -ErrorAction SilentlyContinue
                        if ($driverDesc) {
                            foreach ($pattern in $vmDriverPatterns) {
                                if ($driverDesc.DriverDesc -match $pattern) {
                                    # Replace with generic driver description
                                    $genericDesc = "Standard System Device"
                                    Set-RegistryValue -Path $currentPath -Name "DriverDesc" -Value $genericDesc -Type "String"
                                    Write-ModuleLog "Spoofed driver description: $currentPath" "Debug"
                                    $spoofedCount++
                                    break
                                }
                            }
                        }
                    }
                    catch {
                        # Continue processing other drivers
                    }
                }
            }
        }
        
        Write-ModuleLog "Driver spoofing completed: $spoofedCount drivers modified" "Info"
        return @{ Success = $true; Message = "Spoofed $spoofedCount VM drivers" }
    }
    catch {
        Write-ModuleLog "Driver spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Service Management Functions
function Invoke-ServiceSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting service spoofing..." "Info"
    
    try {
        # VM-related services to disable/remove
        $vmServices = @(
            'VBoxGuest', 'VBoxMouse', 'VBoxService', 'VBoxSF', 'VBoxVideo',
            'vmci', 'vmhgfs', 'vmmouse', 'vmrawdsk', 'vmusbmouse', 'vmvss',
            'vmscsi', 'vmxnet', 'vmxnet3', 'VMTools', 'vm3dservice',
            'VGAuthService', 'VMUSBArbService'
        )
        
        $processedCount = 0
        foreach ($serviceName in $vmServices) {
            try {
                $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
                if ($service) {
                    # Stop the service if running
                    if ($service.Status -eq 'Running') {
                        Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
                        Write-ModuleLog "Stopped VM service: $serviceName" "Debug"
                    }
                    
                    # Disable the service
                    Set-Service -Name $serviceName -StartupType Disabled -ErrorAction SilentlyContinue
                    Write-ModuleLog "Disabled VM service: $serviceName" "Debug"
                    $processedCount++
                }
                
                # Remove service registry entries
                $serviceRegPath = "HKLM:\SYSTEM\CurrentControlSet\Services\$serviceName"
                if (Test-Path $serviceRegPath) {
                    Remove-Item -Path $serviceRegPath -Recurse -Force -ErrorAction SilentlyContinue
                    Write-ModuleLog "Removed service registry: $serviceRegPath" "Debug"
                }
            }
            catch {
                Write-ModuleLog "Failed to process service $serviceName`: $($_.Exception.Message)" "Warning"
            }
        }
        
        Write-ModuleLog "Service spoofing completed: $processedCount services processed" "Info"
        return @{ Success = $true; Message = "Processed $processedCount VM services" }
    }
    catch {
        Write-ModuleLog "Service spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Process Spoofing Functions
function Invoke-ProcessSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting process spoofing..." "Info"
    
    try {
        # VM-related processes to terminate
        $vmProcesses = @(
            'VBoxService', 'VBoxTray', 'vmtoolsd', 'vmware-vmx',
            'vmware-authd', 'vmacthlp', 'vm3dservice', 'VGAuthService'
        )
        
        $terminatedCount = 0
        foreach ($processName in $vmProcesses) {
            try {
                $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
                foreach ($process in $processes) {
                    $process.Kill()
                    Write-ModuleLog "Terminated VM process: $($process.Name) (PID: $($process.Id))" "Debug"
                    $terminatedCount++
                }
            }
            catch {
                Write-ModuleLog "Failed to terminate process $processName`: $($_.Exception.Message)" "Warning"
            }
        }
        
        Write-ModuleLog "Process spoofing completed: $terminatedCount processes terminated" "Info"
        return @{ Success = $true; Message = "Terminated $terminatedCount VM processes" }
    }
    catch {
        Write-ModuleLog "Process spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Main System Spoofing Function
function Invoke-SystemSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting comprehensive system spoofing..." "Info"
    $results = @()
    
    # Execute system spoofing modules
    if ($Config.Modules.System.BIOS.Enabled) {
        $results += Invoke-BIOSSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Drivers.Enabled) {
        $results += Invoke-DriverSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Services.Enabled) {
        $results += Invoke-ServiceSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Processes.Enabled) {
        $results += Invoke-ProcessSpoofing -Config $Config
    }
    
    $successCount = ($results | Where-Object { $_.Success }).Count
    $totalCount = $results.Count
    
    Write-ModuleLog "System spoofing completed: $successCount/$totalCount successful" "Info"
    
    return @{
        Success = $successCount -eq $totalCount
        Results = $results
        Summary = "System spoofing: $successCount/$totalCount modules successful"
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Invoke-SystemSpoofing',
    'Invoke-BIOSSpoofing',
    'Invoke-DriverSpoofing',
    'Invoke-ServiceSpoofing',
    'Invoke-ProcessSpoofing'
)
