@{
    RootModule = 'CPUSpoofing.psm1'
    ModuleVersion = '2.0.0'
    CompatiblePSEditions = @('Desktop', 'Core')
    GUID = 'f2a3b4c5-d6e7-8901-2345-f01234567890'
    Author = 'Cybersecurity Research Team'
    CompanyName = 'Anti-VM Detection Toolkit'
    Copyright = '(c) 2025 Cybersecurity Research Team. All rights reserved.'
    Description = 'Hardware CPU Spoofing Module - Implements comprehensive CPU characteristics spoofing.'
    PowerShellVersion = '5.1'
    ClrVersion = '4.0'
    
    FunctionsToExport = @(
        'Initialize-CPUSpoofing',
        'Invoke-CPUSpoofing',
        'Get-CPUSpoofingResults'
    )
    
    CmdletsToExport = @()
    VariablesToExport = @()
    AliasesToExport = @()
    
    PrivateData = @{
        PSData = @{
            Tags = @('AntiVM', 'CPUSpoofing', 'Cybersecurity', 'Research')
            ReleaseNotes = 'CPUSpoofing Module v2.0 - Modular architecture implementation'
            ExternalModuleDependencies = @()
        }
    }
}
